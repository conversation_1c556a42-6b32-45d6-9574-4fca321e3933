<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: #ffffff; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1132px" height="324px" viewBox="-0.5 -0.5 1132 324" content="&lt;mxfile host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:143.0) Gecko/20100101 Firefox/143.0&quot; version=&quot;28.2.5&quot; scale=&quot;1&quot; border=&quot;0&quot;&gt;&#xA;  &lt;diagram name=&quot;Page-1&quot; id=&quot;KSMvvk7MSD2y6wT4ytVa&quot;&gt;&#xA;    &lt;mxGraphModel dx=&quot;2210&quot; dy=&quot;795&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;850&quot; pageHeight=&quot;1100&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#xA;      &lt;root&gt;&#xA;        &lt;mxCell id=&quot;0&quot; /&gt;&#xA;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-149&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=3;edgeStyle=elbowEdgeStyle;elbow=vertical;strokeColor=#C0BFBC;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;95&quot; y=&quot;369&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;802&quot; y=&quot;503&quot; as=&quot;targetPoint&quot; /&gt;&#xA;            &lt;Array as=&quot;points&quot;&gt;&#xA;              &lt;mxPoint x=&quot;457&quot; y=&quot;454&quot; /&gt;&#xA;            &lt;/Array&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-150&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=3;edgeStyle=elbowEdgeStyle;elbow=vertical;strokeColor=#C0BFBC;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;519&quot; y=&quot;380&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;833&quot; y=&quot;504&quot; as=&quot;targetPoint&quot; /&gt;&#xA;            &lt;Array as=&quot;points&quot;&gt;&#xA;              &lt;mxPoint x=&quot;677&quot; y=&quot;401&quot; /&gt;&#xA;            &lt;/Array&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-5&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;550&quot; y=&quot;230&quot; width=&quot;480&quot; height=&quot;130&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-1&quot; value=&quot;Zone 0&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#9a9996, #1a1a1a);strokeColor=#666666;gradientColor=none;gradientDirection=west;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-5&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;360&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-2&quot; value=&quot;Zone 1&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#9a9996, #1a1a1a);strokeColor=#666666;gradientColor=none;gradientDirection=west;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-5&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;240&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-3&quot; value=&quot;Zone 2&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#9a9996, #1a1a1a);strokeColor=#666666;gradientColor=none;gradientDirection=west;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-5&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;120&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-4&quot; value=&quot;Zone 3&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#9a9996, #1a1a1a);strokeColor=#666666;gradientColor=none;gradientDirection=west;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-5&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-73&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;19RoNj6UBnC00J0asnKw-5&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;283&quot; y=&quot;90&quot; width=&quot;77&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-74&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-73&quot; source=&quot;19RoNj6UBnC00J0asnKw-82&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;25.666666666666668&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;15.400000000000006&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-75&quot; value=&quot;1&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-74&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0131&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-76&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-73&quot; source=&quot;19RoNj6UBnC00J0asnKw-82&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;32.083333333333336&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;30.799999999999997&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-77&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-76&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-78&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-73&quot; source=&quot;19RoNj6UBnC00J0asnKw-82&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;64.16666666666667&quot; y=&quot;30&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;61.599999999999994&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-79&quot; value=&quot;3&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-78&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0063&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-80&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-73&quot; source=&quot;19RoNj6UBnC00J0asnKw-82&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;55.824999999999996&quot; y=&quot;50&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;46.199999999999996&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-81&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-80&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-82&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-73&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;77&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-83&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;19RoNj6UBnC00J0asnKw-5&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;403&quot; y=&quot;90&quot; width=&quot;77&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-84&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-83&quot; source=&quot;19RoNj6UBnC00J0asnKw-92&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;25.666666666666668&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;15.400000000000006&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-85&quot; value=&quot;1&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-84&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0131&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-86&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-83&quot; source=&quot;19RoNj6UBnC00J0asnKw-92&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;32.083333333333336&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;30.799999999999997&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-87&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-86&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-88&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-83&quot; source=&quot;19RoNj6UBnC00J0asnKw-92&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;64.16666666666667&quot; y=&quot;30&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;61.599999999999994&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-89&quot; value=&quot;3&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-88&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0063&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-90&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-83&quot; source=&quot;19RoNj6UBnC00J0asnKw-92&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;55.824999999999996&quot; y=&quot;50&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;46.199999999999996&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-91&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-90&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-92&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-83&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;77&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-6&quot; value=&quot;&amp;lt;div&amp;gt;Accumulator&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#b1ddf0;strokeColor=#10739e;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;150&quot; y=&quot;230&quot; width=&quot;400&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-11&quot; value=&quot;&amp;lt;div&amp;gt;Camera&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#b1ddf0;strokeColor=#10739e;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;30&quot; y=&quot;230&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-12&quot; value=&quot;Intake&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#b1ddf0;strokeColor=#10739e;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-90&quot; y=&quot;230&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-32&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-70&quot; y=&quot;310&quot; width=&quot;77&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-18&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-32&quot; source=&quot;19RoNj6UBnC00J0asnKw-17&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;25.666666666666668&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;15.400000000000006&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-25&quot; value=&quot;1&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-18&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0131&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-19&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-32&quot; source=&quot;19RoNj6UBnC00J0asnKw-17&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;32.083333333333336&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;30.799999999999997&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-26&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-19&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-23&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-32&quot; source=&quot;19RoNj6UBnC00J0asnKw-17&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;64.16666666666667&quot; y=&quot;30&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;61.599999999999994&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-29&quot; value=&quot;3&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-23&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0063&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-24&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-32&quot; source=&quot;19RoNj6UBnC00J0asnKw-17&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;55.824999999999996&quot; y=&quot;50&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;46.199999999999996&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-28&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-24&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-17&quot; value=&quot;Sensor&amp;lt;br&amp;gt;Intake&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-32&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;77&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-33&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;50&quot; y=&quot;310&quot; width=&quot;77&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-34&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-33&quot; source=&quot;19RoNj6UBnC00J0asnKw-42&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;25.666666666666668&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;15.400000000000006&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-35&quot; value=&quot;1&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-34&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0131&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-36&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-33&quot; source=&quot;19RoNj6UBnC00J0asnKw-42&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;32.083333333333336&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;30.799999999999997&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-37&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-36&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-38&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-33&quot; source=&quot;19RoNj6UBnC00J0asnKw-42&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;64.16666666666667&quot; y=&quot;30&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;61.599999999999994&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-39&quot; value=&quot;3&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-38&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0063&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-40&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-33&quot; source=&quot;19RoNj6UBnC00J0asnKw-42&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;55.824999999999996&quot; y=&quot;50&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;46.199999999999996&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-41&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-40&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-42&quot; value=&quot;Sensor&amp;lt;br&amp;gt;Camera&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-33&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;77&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-43&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;473&quot; y=&quot;320&quot; width=&quot;77&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-44&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-43&quot; source=&quot;19RoNj6UBnC00J0asnKw-52&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;25.666666666666668&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;15.400000000000006&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-45&quot; value=&quot;1&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-44&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0131&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-46&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-43&quot; source=&quot;19RoNj6UBnC00J0asnKw-52&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;32.083333333333336&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;30.799999999999997&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-47&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-46&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-48&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-43&quot; source=&quot;19RoNj6UBnC00J0asnKw-52&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;64.16666666666667&quot; y=&quot;30&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;61.599999999999994&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-49&quot; value=&quot;3&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-48&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0063&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-50&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-43&quot; source=&quot;19RoNj6UBnC00J0asnKw-52&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;55.824999999999996&quot; y=&quot;50&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;46.199999999999996&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-51&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-50&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-52&quot; value=&quot;Sensor N&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-43&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;77&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-53&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;593&quot; y=&quot;320&quot; width=&quot;77&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-54&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-53&quot; source=&quot;19RoNj6UBnC00J0asnKw-62&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;25.666666666666668&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;15.400000000000006&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-55&quot; value=&quot;1&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-54&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0131&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-56&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-53&quot; source=&quot;19RoNj6UBnC00J0asnKw-62&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;32.083333333333336&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;30.799999999999997&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-57&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-56&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-58&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-53&quot; source=&quot;19RoNj6UBnC00J0asnKw-62&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;64.16666666666667&quot; y=&quot;30&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;61.599999999999994&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-59&quot; value=&quot;3&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-58&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0063&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-60&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-53&quot; source=&quot;19RoNj6UBnC00J0asnKw-62&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;55.824999999999996&quot; y=&quot;50&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;46.199999999999996&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-61&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-60&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-62&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-53&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;77&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-63&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;713&quot; y=&quot;320&quot; width=&quot;77&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-64&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-63&quot; source=&quot;19RoNj6UBnC00J0asnKw-72&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;25.666666666666668&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;15.400000000000006&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-65&quot; value=&quot;1&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-64&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0131&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-66&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-63&quot; source=&quot;19RoNj6UBnC00J0asnKw-72&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;32.083333333333336&quot; y=&quot;40&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;30.799999999999997&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-67&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-66&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-68&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-63&quot; source=&quot;19RoNj6UBnC00J0asnKw-72&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;64.16666666666667&quot; y=&quot;30&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;61.599999999999994&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-69&quot; value=&quot;3&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-68&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0063&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-70&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-63&quot; source=&quot;19RoNj6UBnC00J0asnKw-72&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;55.824999999999996&quot; y=&quot;50&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;46.199999999999996&quot; y=&quot;60&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-71&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-70&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-72&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-63&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;77&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-113&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;598&quot; y=&quot;494&quot; width=&quot;192&quot; height=&quot;59&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-109&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;orthogonal=1;strokeWidth=3;entryX=1.003;entryY=0.402;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-113&quot; target=&quot;19RoNj6UBnC00J0asnKw-93&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;192&quot; y=&quot;23&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;186&quot; y=&quot;34&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-110&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-109&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-111&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);entryX=0.996;entryY=0.195;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-113&quot; target=&quot;19RoNj6UBnC00J0asnKw-93&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;206&quot; y=&quot;12&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;185&quot; y=&quot;18&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-112&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-111&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-103&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.004;exitY=0.423;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-113&quot; source=&quot;19RoNj6UBnC00J0asnKw-93&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;35&quot; y=&quot;24&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint y=&quot;25&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-104&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-103&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-99&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.002;exitY=0.195;exitDx=0;exitDy=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);exitPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-113&quot; source=&quot;19RoNj6UBnC00J0asnKw-93&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;45.200000000000045&quot; y=&quot;-120&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint y=&quot;11&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-100&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-99&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-93&quot; value=&quot;Pulse 2&amp;lt;br&amp;gt;192.168.21.20&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#f6f5f4, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-113&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;35&quot; width=&quot;114&quot; height=&quot;59&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-118&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;847&quot; y=&quot;491&quot; width=&quot;192&quot; height=&quot;59&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-119&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;orthogonal=1;strokeWidth=3;entryX=1.003;entryY=0.402;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-118&quot; target=&quot;19RoNj6UBnC00J0asnKw-127&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;192&quot; y=&quot;23&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;186&quot; y=&quot;34&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-120&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-119&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-121&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);entryX=0.996;entryY=0.195;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-118&quot; target=&quot;19RoNj6UBnC00J0asnKw-127&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;192&quot; y=&quot;12&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;185&quot; y=&quot;18&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-122&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-121&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-123&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.004;exitY=0.423;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-118&quot; source=&quot;19RoNj6UBnC00J0asnKw-127&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;35&quot; y=&quot;24&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint y=&quot;25&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-124&quot; value=&quot;4&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-123&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0253&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-125&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;exitX=0.002;exitY=0.195;exitDx=0;exitDy=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);exitPerimeter=0;&quot; parent=&quot;19RoNj6UBnC00J0asnKw-118&quot; source=&quot;19RoNj6UBnC00J0asnKw-127&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;45.200000000000045&quot; y=&quot;-120&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;-11&quot; y=&quot;14&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-126&quot; value=&quot;2&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; parent=&quot;19RoNj6UBnC00J0asnKw-125&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-0.0206&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-127&quot; value=&quot;Pulse 1&amp;lt;br&amp;gt;192.168.21.21&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#f6f5f4, #ededed);&quot; parent=&quot;19RoNj6UBnC00J0asnKw-118&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;35&quot; width=&quot;114&quot; height=&quot;59&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-143&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeColor=light-dark(#000000,#EDEDED);strokeWidth=3;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;599&quot; y=&quot;518&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;639&quot; y=&quot;382&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-144&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeColor=light-dark(#000000,#EDEDED);strokeWidth=3;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;788&quot; y=&quot;515&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;760&quot; y=&quot;379&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-145&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeColor=light-dark(#000000,#EDEDED);strokeWidth=3;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;849&quot; y=&quot;516&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;880&quot; y=&quot;380&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-146&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeColor=light-dark(#000000,#EDEDED);strokeWidth=3;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;1039&quot; y=&quot;514&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;1000&quot; y=&quot;380&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;19RoNj6UBnC00J0asnKw-147&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=3;edgeStyle=elbowEdgeStyle;elbow=vertical;strokeColor=#C0BFBC;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;-24&quot; y=&quot;373&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;600&quot; y=&quot;504&quot; as=&quot;targetPoint&quot; /&gt;&#xA;            &lt;Array as=&quot;points&quot;&gt;&#xA;              &lt;mxPoint x=&quot;290&quot; y=&quot;504&quot; /&gt;&#xA;            &lt;/Array&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;      &lt;/root&gt;&#xA;    &lt;/mxGraphModel&gt;&#xA;  &lt;/diagram&gt;&#xA;&lt;/mxfile&gt;&#xA;"><defs/><rect fill="#ffffff" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));" width="100%" height="100%" x="0" y="0"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="19RoNj6UBnC00J0asnKw-149"><g><path d="M 185 139 L 185 224 L 892 224 L 892 273" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(74, 73, 71));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-150"><g><path d="M 609 150 L 609 171 L 923 171 L 923 274" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(74, 73, 71));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-5"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-1"><g><rect x="1000" y="0" width="120" height="60" fill="#9a9996" style="fill: light-dark(rgb(154, 153, 150), rgb(26, 26, 26)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));" stroke="#666666" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 30px; margin-left: 1001px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Zone 0</div></div></div></foreignObject><image x="1001" y="23.5" width="118" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-2"><g><rect x="880" y="0" width="120" height="60" fill="#9a9996" style="fill: light-dark(rgb(154, 153, 150), rgb(26, 26, 26)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));" stroke="#666666" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 30px; margin-left: 881px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Zone 1</div></div></div></foreignObject><image x="881" y="23.5" width="118" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-3"><g><rect x="760" y="0" width="120" height="60" fill="#9a9996" style="fill: light-dark(rgb(154, 153, 150), rgb(26, 26, 26)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));" stroke="#666666" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 30px; margin-left: 761px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Zone 2</div></div></div></foreignObject><image x="761" y="23.5" width="118" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-4"><g><rect x="640" y="0" width="120" height="60" fill="#9a9996" style="fill: light-dark(rgb(154, 153, 150), rgb(26, 26, 26)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));" stroke="#666666" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 30px; margin-left: 641px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Zone 3</div></div></div></foreignObject><image x="641" y="23.5" width="118" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-73"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-74"><g><path d="M 938.32 119.94 L 938.4 150" fill="none" stroke="#986a44" style="stroke: light-dark(rgb(152, 106, 68), rgb(252, 185, 158));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-75"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 939px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">1</div></div></div></foreignObject><image x="936" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAABCklEQVRYR+2VwQ1EQBSGf0UoQROuShEtuIi4iEi0oAsHB6IBFCBxFSUQCbsbyW5WVryYHSdvzs/7Zr73j1Eer4ULl8IAyi4rogyBFclX1HUdqqpaG+u6DlVVDyGnZ2DbNqIoWpvmeQ7DMOQCNE1D27bXAIqi2OxY6gmmaVqd13X9USIN0Pc9TNNEmqYb38KAcRzRNA3KskSWZUiSBMMw/AxTCBCGIRzHwbIsZMaFAK7rwvd9srlwTD3PQxAEu4B5nvH9wgqd4GjrlmUhjmP5KXp3ZACZHFbEinYNnHoyOUWcIk4RwPfgBveAPOJOwam/KQNEDJDf8AxYEWmALFDIij8LGEAKZEU3UPQEa4e4dwht2PoAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-76"><g><path d="M 953.88 119.94 L 953.8 150" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-77"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 954px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="951" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-78"><g><path d="M 984.6 120.06 L 984.6 150" fill="none" stroke="#0000cc" style="stroke: light-dark(rgb(0, 0, 204), rgb(212, 212, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-79"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 985px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">3</div></div></div></foreignObject><image x="982" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAAES0lEQVRYR+1XWSh1bRR+DJEMyZR5ukAZMtwRSZkKKSIlEkIpceFCKEkpKSkXXLigZAyJkFkkQ5lFikyZI2PE93/r1d7f2ec43944f3/9nXV1zn7XXs/7Pu9az1pb49dvw79oGmoAMXbVFIkxBDVFP6NoZmYGvb29mJycxPHxMS4vL6GjowNTU1O4u7sjODgYycnJsLS0VAr06R3s7u4iKysLY2NjojvU1dVFTk4OKisrQb/lTQFgbm4OUVFRuLq6Eg0u6+Dn58c2ZGRkJHhPAEBBvby8cHJyInCytrZGeHg47O3tcXt7i/X1ddBG7u/vBX6RkZEYGBhQDkC0NDQ08A56enqora1FWloatLS0BC8eHR0xGuUD9vX1MQY4409wfX0NW1tbPD09sTUNDQ0MDg4iLCxMKVWk9IGBgaBk4CwkJASjo6OKAM3NzUhJSeEXkpKS0NLSInoPKysr8Pb25v0oy2iz+vr6Hxvl+kF2djbq6+t5x+7ubsTGxooCkIOdnR2IMs6Wlpbg6+srBAgICMDs7CzvdH5+DnNzc0kAoaGhGBkZ4X2JWkoKwQlcXV2xs7PDHmpra+P19VVScHJKTExEe3s77z88PAwCFQAUFhbi7u6OPaSCqampkQwQFBSE6elp3n97exsuLi5CAMnR5BzpQq2srPDy8sJWqNCIXq6qfyzX6enpaGxs5GHz8vIEp/82AFVzUVERqLA4I9FbWFhg9cSZJAAKVlVVhYeHB1xcXLBkOD09FZBlYmLCMsnHx0fwXBLA0NAQIiIilF4T0URqamZmpuCjEgBPT0+UlZWxwiSJkTVJAKSc+fn5IO0hag4PD/H+/q6wW1JTUgDZviAJQD4SpeTW1hYqKirQ0dEhWCYQunhOfb8FIBuxv78f0dHR7HScdXZ2Ii4uTjWFRlFyc3NRV1fHA5CET01NqQ6AUtfCwuJP7v++6OfnZzYg/JgiLioBEBBne3t7cHR0/ADo6urCxMQEv5iZmcl681dMXvCoy/n7+38AlJeXo7S0lI9XXV2NgoKCr8RnFby8vMy/s7i4CJo0GEBTUxNSU1P5RWrysgImhkQZZGBggMfHR96VaoU0iQHI91UPDw+sra2JxeXXSf/d3Nz4/6RLdB+ampofALQDZ2dn7O/v805UQPHx8ZJAEhISBAVH/9va2oRpWlxczCqTM8qA1dVVGBoa/hWEqjYmJkbg82nLPDg4AFHDtU16w8HBgU0aXAOXjUJ8l5SUsOYiq0s0EI+Pj/+pCdnPWPnZiPMifmkooD5L4+LGxgY73c3NjWDn1Drn5+f/3nCoS5G2f/X73MnJCT09PQr182kl01yTkZHBvgnEjOSA0po2ZWxsrOCuVCre3t7YjNna2orNzU02cZ+dnYEGYupc9AFCcyhlmo2NjdJ9qEyLlCGoAcRyQHX9QH0Holz/jyn69tklviicVCW+9BU3NYAoW2qK/nuK/gGxK3KGRMumSQAAAABJRU5ErkJggg=="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-80"><g><path d="M 969.2 120.12 L 969.2 150" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-81"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 970px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="967" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-82"><g><rect x="923" y="90" width="77" height="30" fill="#eeeeee" style="fill: light-dark(rgb(238, 238, 238), rgb(32, 32, 32)); stroke: light-dark(rgb(54, 57, 61), rgb(186, 189, 192));" stroke="#36393d" pointer-events="all"/></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-83"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-84"><g><path d="M 1058.32 119.94 L 1058.4 150" fill="none" stroke="#986a44" style="stroke: light-dark(rgb(152, 106, 68), rgb(252, 185, 158));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-85"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 1059px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">1</div></div></div></foreignObject><image x="1056" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAABCklEQVRYR+2VwQ1EQBSGf0UoQROuShEtuIi4iEi0oAsHB6IBFCBxFSUQCbsbyW5WVryYHSdvzs/7Zr73j1Eer4ULl8IAyi4rogyBFclX1HUdqqpaG+u6DlVVDyGnZ2DbNqIoWpvmeQ7DMOQCNE1D27bXAIqi2OxY6gmmaVqd13X9USIN0Pc9TNNEmqYb38KAcRzRNA3KskSWZUiSBMMw/AxTCBCGIRzHwbIsZMaFAK7rwvd9srlwTD3PQxAEu4B5nvH9wgqd4GjrlmUhjmP5KXp3ZACZHFbEinYNnHoyOUWcIk4RwPfgBveAPOJOwam/KQNEDJDf8AxYEWmALFDIij8LGEAKZEU3UPQEa4e4dwht2PoAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-86"><g><path d="M 1073.88 119.94 L 1073.8 150" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-87"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 1074px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="1071" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-88"><g><path d="M 1104.6 120.06 L 1104.6 150" fill="none" stroke="#0000cc" style="stroke: light-dark(rgb(0, 0, 204), rgb(212, 212, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-89"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 1105px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">3</div></div></div></foreignObject><image x="1102" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAAES0lEQVRYR+1XWSh1bRR+DJEMyZR5ukAZMtwRSZkKKSIlEkIpceFCKEkpKSkXXLigZAyJkFkkQ5lFikyZI2PE93/r1d7f2ec43944f3/9nXV1zn7XXs/7Pu9az1pb49dvw79oGmoAMXbVFIkxBDVFP6NoZmYGvb29mJycxPHxMS4vL6GjowNTU1O4u7sjODgYycnJsLS0VAr06R3s7u4iKysLY2NjojvU1dVFTk4OKisrQb/lTQFgbm4OUVFRuLq6Eg0u6+Dn58c2ZGRkJHhPAEBBvby8cHJyInCytrZGeHg47O3tcXt7i/X1ddBG7u/vBX6RkZEYGBhQDkC0NDQ08A56enqora1FWloatLS0BC8eHR0xGuUD9vX1MQY4409wfX0NW1tbPD09sTUNDQ0MDg4iLCxMKVWk9IGBgaBk4CwkJASjo6OKAM3NzUhJSeEXkpKS0NLSInoPKysr8Pb25v0oy2iz+vr6Hxvl+kF2djbq6+t5x+7ubsTGxooCkIOdnR2IMs6Wlpbg6+srBAgICMDs7CzvdH5+DnNzc0kAoaGhGBkZ4X2JWkoKwQlcXV2xs7PDHmpra+P19VVScHJKTExEe3s77z88PAwCFQAUFhbi7u6OPaSCqampkQwQFBSE6elp3n97exsuLi5CAMnR5BzpQq2srPDy8sJWqNCIXq6qfyzX6enpaGxs5GHz8vIEp/82AFVzUVERqLA4I9FbWFhg9cSZJAAKVlVVhYeHB1xcXLBkOD09FZBlYmLCMsnHx0fwXBLA0NAQIiIilF4T0URqamZmpuCjEgBPT0+UlZWxwiSJkTVJAKSc+fn5IO0hag4PD/H+/q6wW1JTUgDZviAJQD4SpeTW1hYqKirQ0dEhWCYQunhOfb8FIBuxv78f0dHR7HScdXZ2Ii4uTjWFRlFyc3NRV1fHA5CET01NqQ6AUtfCwuJP7v++6OfnZzYg/JgiLioBEBBne3t7cHR0/ADo6urCxMQEv5iZmcl681dMXvCoy/n7+38AlJeXo7S0lI9XXV2NgoKCr8RnFby8vMy/s7i4CJo0GEBTUxNSU1P5RWrysgImhkQZZGBggMfHR96VaoU0iQHI91UPDw+sra2JxeXXSf/d3Nz4/6RLdB+ampofALQDZ2dn7O/v805UQPHx8ZJAEhISBAVH/9va2oRpWlxczCqTM8qA1dVVGBoa/hWEqjYmJkbg82nLPDg4AFHDtU16w8HBgU0aXAOXjUJ8l5SUsOYiq0s0EI+Pj/+pCdnPWPnZiPMifmkooD5L4+LGxgY73c3NjWDn1Drn5+f/3nCoS5G2f/X73MnJCT09PQr182kl01yTkZHBvgnEjOSA0po2ZWxsrOCuVCre3t7YjNna2orNzU02cZ+dnYEGYupc9AFCcyhlmo2NjdJ9qEyLlCGoAcRyQHX9QH0Holz/jyn69tklviicVCW+9BU3NYAoW2qK/nuK/gGxK3KGRMumSQAAAABJRU5ErkJggg=="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-90"><g><path d="M 1089.2 120.12 L 1089.2 150" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-91"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 1090px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="1087" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-92"><g><rect x="1043" y="90" width="77" height="30" fill="#eeeeee" style="fill: light-dark(rgb(238, 238, 238), rgb(32, 32, 32)); stroke: light-dark(rgb(54, 57, 61), rgb(186, 189, 192));" stroke="#36393d" pointer-events="all"/></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-6"><g><rect x="240" y="0" width="400" height="60" fill="#b1ddf0" style="fill: light-dark(rgb(177, 221, 240), rgb(23, 61, 77)); stroke: light-dark(rgb(16, 115, 158), rgb(84, 169, 206));" stroke="#10739e" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 398px; height: 1px; padding-top: 30px; margin-left: 241px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><div>Accumulator</div></div></div></div></foreignObject><image x="241" y="23.5" width="398" height="17" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABjgAAABECAYAAAA80zznAAAa6ElEQVR4Xu3dB5AtWVkH8EVylAxLfKwkKRBY4oLAA0RwWcKSkTSISwYRyZKD5JwkKSAgOaclLkGyEoUl+sgZyewi6ftb07XHtu/c2zP3MtNvfqfq1HQ4fbr71z1V7/U353zHO0AhQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECExM4HgTu16XS4AAAQIECBAgQIAAAQIECBAgQIAAAQIECBA4QIDDS0CAAAECBAgQIECAAAECBAgQIECAAAECBAhMTkCAY3KPzAUTIECAAAECBAgQIECAAAECBAgQIECAAAECAhzeAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQGByAgIck3tkLpgAAQIECBAgQIAAAQIECBAgQIAAAQIECBAQ4PAOECBAgAABAgQIECBAgAABAgQIECBAgAABApMTEOCY3CNzwQQIECBAgAABAgQIECBAgAABAgQIECBAgIAAh3eAAAECBAgQIECAAAECBAgQIECAAAECBAgQmJyAAMfkHpkLJkCAAAECBAgQIECAAAECBAgQIECAAAECBAQ4vAMECBAgQIAAAQIECBAgQIAAAQIECBAgQIDA5AQEOCb3yFwwAQIECBAgQIAAAQIECBAgQIAAAQIECBAgIMDhHSBAgAABAgQIENhOgYPq5F8cuIAf1bYzVT12Oy/OuXedwDvrjvc2d321Wj5y1ym4YQIECBAgQIAAAQITERDgmMiDcpkECBAgQIAAgf1U4H51Xw+ecW/Xqe2v2k/v223tTAEBjp35XFwVAQIECBAgQIAAgUEBAQ4vBgECBAgQIECAwHYKHF0nP9+MC3h5bb/+dl6cc+86AQGOXffI3TABAgQIECBAgMCUBQQ4pvz0XDsBAgQIECBAYNoCF6vL/8gGt3BM7cs0VT+e9m26+gkJ7NQAx7PK8LDG8Za1/OYJubpUAgQIECBAgAABAisREOBYCatOCRAgQIAAAQIEFhB4fLW5S9Put7Xc//fpWm173gJ9aUJgGQI7NcDxsrq56zU3eO1afs0yblgfBAgQIECAAAECBKYsIMAx5afn2gkQIECAAAEC0xU4fl3616qeubmFF9fy3t62t9T6Vad7m658YgICHBN7YC6XAAECBAgQIEBgdwsIcOzu5+/uCRAgQIAAAQLbJXCVOnGCF205tFauXvUOzcZf1/JZq357uy7UeXeVgADHrnrcbpYAAQIECBAgQGDqAgIcU3+Crp8AAQIECBAgME2BTDt18+bSv7MeyLhs/Tyqd0t3rvUnT/M2XfXEBAQ4JvbAXC4BAgQIECBAgMDuFhDg2N3P390TIECAAAECBLZD4KR10ozIOGVz8gQwEsj4g6pfr9pOXfWBWj9kOy7UOXedgADHrnvkbpgAAQIECBAgQGDKAgIcU356rp0AAQIECBAgME2BG9ZlJ99GWy5VKx9a3/CU+tlOU5XNf1T1S1u83eT9uHzV61e9ZNWzVD1t1Ywe+WbVf6v6iqrvq5qE52PLqvsfez3Lap8pwv666p9WvWDVU1RNEOqTVTMS501VM5VYvxxcG25cNdORnanqaap+pepnq36q6hOq7rSpx7YS4EjA7hpVr1A179cZqp5uHeUn9TPvWMxyjldV/cGA2axNy0oyfuk6wbWq7q164PpzOaZ+fqvqf1XNs3xd1X0jrm1e0zz/v6t6oaoxekjVR887yH4CBAgQIECAAAECiwgIcCyipA0BAgQIECBAgMAyBfIB9bCmwy/U8nma9XwgPqp3wvvW+sO2cBH5OP/0qvlAP698rBrcrmpGjixalt1/Ajpx6crRtfDHi15MtUtQIkncu7KvFs414/hb1fZnN/vybN5Q9cRVH1f11lVPsMG531b7ErTqPtjnI/Zjqx4x53p/VPsfWDWBjo3Kv9TOmzYNblTLL5lzTLv7WbWSAE1X1mohgZmhspkAx0mqo3tVzQikBHEWKQkqxCgf+48dOCCBke8t0tF6m7vUzydu0P5i6/szBdy88ptqkPfhflUT/JtXErx4TNNoby2/q2remedWvUmvgwQ3E/hSCBAgQIAAAQIECGxZQIBjy4Q6IECAAAECBAgQGCGQD7f5S/YTNsc8qJYf2KwPTVP1mdp/gRHnaZveu1YSHBnzb9+M4Lh71XyEnldW0f92Bzj+vW769VXzYXyRkqBQRuEkuHFU1UUCSV2/a7UwK+CQNjs5wJGp1F69fu+LOPXbvL02JKCUgEdblhngSIAqwb38Xo0p363GV6360TkHDQU43lvHJJBxvYFjBTjGPAVtCRAgQIAAAQIENhQY8588lAQIECBAgAABAgS2KpCREU/rdXLeWv98b9tTa/32vW0XqfWPj7yA+1T7oZEfP6vtmSopowjOXfVsVYf+bZxryMfhWWVV/W9ngCNTeN2tagIWKRmZcWTVBKb2VL1E1bMPgMQpUzO1QZF8HM9ImASMEvRInxkZ0pbv10pGp+SD+lDZyQGOjF658sBF5/3KCJpM5ZWcMxlRk5ppzPrl4bUh71FblhXgyPub36Wh8sXamCnDMgIlvwOZUqtf8vtxxaobBTmGAhxXqmPuP+O8AhwzYGwmQIAAAQIECBAYLyDAMd7MEQQIECBAgAABApsXSJ6LyzSHJ+9G9yG97XVomqpHVYN7jjh1po16d9X237w/XO/jhfUzH6G7klwcGUmSAEz7ETptzlk1H+H7ZZX9b2eAI7kYuumsMlXRnaq2Iwwy+ibTF2XasFn/n4jXzau+sYeWUTgvqHrR3vY71vqsD/E7NcBxeF3zK3v3kaBZpp3KFFq/6u1LHpJMlZVgxsmafT+t5QQX+sbJF9OVB9TC5Xrr+V3qyudq4au98yWglJE4J+ptzzPN71IbVMzojvzOPbJqAlhtSb6UBK1+2dverfYDHBnR9NCq7e9RRmB9pGqmvMrv/Etn9GUzAQIECBAgQIAAgVECAhyjuDQmQIAAAQIECBDYgkA+mvcThSdvwZMH+hyapiofcBNsWCQBeD7qJqFzRod05Ru1kBwE+za4h6GP1vlYmw/6bVl1/9sZ4Ojuc9az6fY/vxZuNmCZ5xTn/gf3rmlGfyQQkJENXUlwI0GOobJTAxzvqYtNkKsrGbGR4EASdm9UDq2dr63aBgAyCuQdGxy0mSTj76/+klS8K8mtkXwYGUExq+SanlP1Fr0Gf1/r/zDjoH6AIzlFulE6SVqegE6mMFMIECBAgAABAgQILF1AgGPppDokQIAAAQIECBCYIZCPpAkWdOXXtXCWqrMSGQ9NU5W/as+H5XklSa/bD7n5uJuPvR+ed2Dtf0bV5C3oSgIjmV6oLavuf7sDHDFuRxAMsfWvsWtz3Vroj2zoH5+E2AmgdCUf94emesr+nRjgSADu51Xb6bb+stb/dYH3K00ymqGdyiujXXKfs8rYAEfe9QQ42pIRN0PTtfXPmeTgSRLejrTK78A5quZ3tl/6AY5uf6aiS9BqkYDkgmyaESBAgAABAgQIEPi/AgIc3ggCBAgQIECAAIHfl0CmqTl/c7L8dXf+mn1W2Vs73tnb+Y+1nmmk5pUcl+O7ktEG/b9Kn9VH8kF8urdzT61/udm26v63O8Bx8brXTG80r/ykGpyiaZRpk9pRDbOOX6sd/9zszDRIF5rReCcGODKlVBuYy0f8M1b93jyw9f3PrJ9HNG1vW8sJrM0qYwMcz62O2vc9+TYyPdisaab6583z7wcDr1XbMvKkX4YCHBmxkXws/7Ogh2YECBAgQIAAAQIENiUgwLEpNgcRIECAAAECBAiMFDi42vc/mN+0tiUXxqwyNE1VcjscOOfD6VCC5kPqmCS7XrQkmJG/WO/KVWohCaVTVt1/zrGdAY4ELU61INQnql0bmMg0RhmpM69kdEhGCXRlagGO5CFppz/LyIaj5910s3/VAY5Mk5WcH11J7prk3RhTPliNE6ToSkZk3GGgg6EAR6bCetGYk2lLgAABAgQIECBAYDMCAhybUXMMAQIECBAgQIDAWIHH1gF3bQ5K8u58gG0TfQ/1OTRN1dWrYT95dXtsRoW8odnwzVrOFFNjpso5qNqfsukjAY8kKE9Zdf85x3YGODK1UTs90UbPOn/ln7/278qNaiEJtueVJJZvA05TC3DMu7+N9idwl2DfRZpGyxzBkTw1+3oXcJ5a/8LIi757tW+DInnWbcCj664f4Ehy9QTIfjHyfJoTIECAAAECBAgQGC0gwDGazAEECBAgQIAAAQIjBfJBNwmnk2+jKxm5kREc88reatCfpmresQ+qY+7fdPz6Wr7GvBON2L/q/nMp2xngSB6J5JNYpPQDHJerg967wIG7NcBx4bLJCJfr94yWGeC4TvX9iqb/jC45SdUEHsaU/u9eprc6WdV+Ho5+gOPj1aYN3ow5p7YECBAgQIAAAQIERgkIcIzi0pgAAQIECBAgQGATAn9Wx7y1d1xGQSQHx7wyNE1VRn0k30GSPA+Vf6qNt2x2PLqW7zHvRCP2r7r/XMp2BjheUOe/2YIe/QBH8m8kD8e8sj8HOE5eN3++qudef47nqp+ZzirBjVPPgFlmgOM2dY7kqunKV2ohozrGlvyOfbt30OlrPdPEtaUf4Hh17Tx87Mm0J0CAAAECBAgQILAZAQGOzag5hgABAgQIECBAYIxAkkmvjTlggbY3rjYvntHuVbX92s2+e9fyIxboc9Emq+4/1yHAcdzT2IlJxvvvSnK03KBqcotkOqix/89aZoAj+Tba9/0969e16PvdtTtBLfSThCdok4TlbekHOJ5bO9sA49jzak+AAAECBAgQIEBgYYGx//BeuGMNCRAgQIAAAQIECJRApsbJX4EvmrR6UbTXVcNrzmj8jtp+xWbfnWr5KYt2vEC7VfefSxDgOO5B7OQAx8F1mU+smpEri5b/rIYJHKwqB8dDq+820fvLa70/Jdai13pMNTxx0/hitfwfvYP7AY6MHrndoifQjgABAgQIECBAgMBWBAQ4tqLnWAIECBAgQIAAgXkC+av2RZJOz+unvz8fiM9c9QcDB2bqq6s12x9Qyw8ee4IN2q+6/5xagOO4B7BTAxxXrUt8bdUTzXhXflrbP1M1yb0/W/WTVTOlV/LRPLnqHZvjljmC417V78Obvt9dy1fYxPuf6eGSt6P9P+MF1u+p7U6AYxO4DiFAgAABAgQIEFiOgADHchz1QoAAAQIECBAgMCzwmtrcjrT4Ta3ng+vYco464KDeQck18MyBjl5U2zKFVVeSM+NWY0+4QftV959TbzXAcf7qIx/Xu7KvFpILYqjE5tnNjv0tB0emMrthc39rtfy8GRZJaL+32ZdA2ZEDbZNT46NVk3S7LQliZATD+6pmuZ+Qu2u7ygBHPwfHl+uke2bc70abT1M7+wHEA2vbt3oHCXBsAtchBAgQIECAAAECyxEQ4FiOo14IECBAgAABAgT+v8Bp1z+GnrDZ9ZZazl++jy2ZAii5BNryrlrZO9DRk2pbpqXqyttqITkSllVW3X+uc6sBjsOqj0zj1ZV9tbBbAxwfqXvP1EpdWauFrQY4nlF93LrpM4GMBIqeX/W3C7xoqwxwXLfOn2mpupJryzRTs4Itsy73kNqRQE1Xcl8nrXps7wABjgUeuCYECBAgQIAAAQKrERDgWI2rXgkQIECAAAECBA44INPuPL0HcfNaz5RDY0v+3Zq/RD97c2A+uGZkx9d6nSXBcUZtdCVJkZMceUy5RTVeaw54dS0n10LKqvvPObYa4Pjb6uNxzfXvq+XdGuD4Yd37HzYWea5bCXDkXcz0U+3ojfvUejst1Lx3LTlh7tA0WuYUVRnp1E8EPpQcfN413rl559M2uUMuOHCQAMc8SfsJECBAgAABAgRWJiDAsTJaHRMgQIAAAQIEdr1ARly0yZd/XutnqpqPw5spj6yD7tE78O61/pjetv70TNl9vqqfG3HSo6ptm7cgH3G7gMGq+89l9gMcyeNwnhHXnwBPAjFd2VcL+0uA46Z1Ly9c0CIBsATG2rJWK1sJcJyljv96r889A+fZ6BJfWTsPbxosM8CRbr9T9QxN/3er5ccuaNY16//+ZhqzIwb6EOAYCas5AQIECBAgQIDA8gQEOJZnqScCBAgQIECAAIHjBPbU4peqtv/eTO6Km2wB6cJ17Md6xycPwsG9bTlnzp1r6Eo+7uYj7yIlf+3/vaonaBpniqtMdZWy6v5zjn6AI0nVT141P+eVs1aDBERO0jTcV8v7S4DjgXUvD5qHsL6/PxVUNq9V3UqA4xJ1/Iea8+eZZAqoRaamymFJ3v2VqnlOXVl2gKOfmD3BvYy+WOT9yTVdpGp+t9pyg1p52YC7AMeCL6NmBAgQIECAAAECyxcQ4Fi+qR4JECBAgAABAgQOOCBT9jysB3Forb9piziZJucCvT6y3ibUzu57Vn1E0+6XtfwnVT+7wPkfX23u0rTLFEdnrNp+HF51/2er8321d62XrPUPL3D9/fwQOWRf1akGODJyoE0S/8Zav/oCDuesNvmwf6Je27Va30qAI6OBju71mYBUgmqLlPtWo4f0Gi47wDGUs+Zedc6MgppXjl8Nkt/msk3DJBbPaJihAIkAxzxR+wkQIECAAAECBFYmIMCxMlodEyBAgAABAgR2tUA/EJEpc/IX67/aosrQx+F8LL5/r9/T13pGMbS5F5KX4HJVv7nBNVyp9r21av7KvivJI3L733P/ScyeKb3aUSRJ+Jzr/80G158P/8kX0h6X5vuqTjXAkdEa/ef7F7XtzRs4JBl2kqxfeaDNWm3bSoAjI2N+UDXn6ErybySot1HJO5X7eMBAo+TjeNoGB2fkxPWa/bNGU7Rd9JOrJ8n4Dau+YoPzJLiRgFKM2pLfu37AstsvwDHnwdtNgAABAgQIECCwOgEBjtXZ6pkAAQIECBAgsFsFhqa3eVJh/M0SQPpTN6XLWUnE+8nA0/a7VZPH4yVVf9FcT6Z/ygfq5PRIcKErP6uF5L4YCoqsuv9MidX/QJ/cDXHsJ1bPR/d8aM4H9IxYyMfsTLOVnCcp+6pONcAxNBrh+3U/SaQ+lLA+U5ZlFMvF1+89+TLa6aDWan0rAY50+5qq1zzuNfnf6akyBdpTqx7bbO8W99bCQ6u2oyLaZrPyW3Rt+gGOB9eOoUBJ22emdMuIn/Z9znUmYPfo9Xeia5//F8Y52y/Vu/5P1Hqm5cooqKEiwDEDxmYCBAgQIECAAIHVCwhwrN7YGQgQIECAAAECu00gSb/z0bMti06vtIjVB6pR/yNs1tu8CF0/L6iFobwfSXT+6ar/XfWg9Zq/Xu+XW9SG529wUavs/9J13ndXbT9Q51IysiMGmZYrQZo9Vf+86qma60wQJKMcrra+bV/9nGqAI7fQDyh0t5opx5KXJfeX0Tp5Dy7aOCT4FYvXN9vWanmrAY588P9g1f7/p75R296/fj05ZRKSX75qG2A5ptYz+uTazTVlVM6zqmbURd7jBBXaktEdt2s2JNjw4qrJ5ZGRJAmGDY1oyb0/oddXVhPoyDRbmQYtwbEE8Q4caJeRKleo+qmBfd0mAY4NcOwiQIAAAQIECBBYrYAAx2p99U6AAAECBAgQ2G0CQwmUkwcheQuWVe5cHT2x11nW27wZ3e5cT/46PqMtxpR8cE6ejQRrNiqr7v+udfIkSB9TkkMkxyXfyf4S4DhN3UsCGckDsWhJsCFTjp26agJCXVmrha0GONJXRvs8atGLWW+XgMThVROk6ueN6bq6Yy1kJEhb/qpWnrPBufLu938nuuaZXu0pVcf+3y+jlhI42yi4kXMIcIx8CTQnQIAAAQIECBBYnsDYf+Qu78x6IkCAAAECBAgQ2B8F8kH57b0by7RJ/aTKW7n3M9fBmaKpHXGRJMhJzJ2pmYZKcg8kWJE280pGduQv3/NX8YuWVfZ/RF1E8lAM/YV9e30ZlXKbqi9a37g/BThySxlpkyTZbS6KWc/nyNpxs6qZkiyjOlYR4Mi5D636uKrzAngZtZGRFMnV8eP1i850Ywl29MtQgCM5VY6qOmuKq40CHOk/I6hy/kNmgTXb8zuUKb4yBVamOZtXBDjmCdlPgAABAgQIECCwMgEBjpXR6pgAAQIECBAgQGCHCWQqnoxoyEflTGOUQEn+uv+HVfPX6u+t+sb1mil8xpZV9n+yupjbVs3USJlOKDUBnuSi+HLVl1ZNXpF80N/fSwIWCV6cd90h00AlaJCP8e+pmmnD8nMzz3AzdplC7FZVD6uavBenq5oRQAm6fb5qkp0nmJH1tpyyVpL35bpVz141uTsyqiMJvftBwhzXvQM3ruU9VfPu/qRqnn+CEa9d4OIvU22SO2Rv1QTMzlg1013l2r5UNe9/pgPLSBOFAAECBAgQIECAwI4XEODY8Y/IBRIgQIAAAQIECBAgQIAAAQIECBAgQIAAAQJ9AQEO7wQBAgQIECBAgAABAgQIECBAgAABAgQIECAwOQEBjsk9MhdMgAABAgQIECBAgAABAgQIECBAgAABAgQICHB4BwgQIECAAAECBAgQIECAAAECBAgQIECAAIHJCQhwTO6RuWACBAgQIECAAAECBAgQIECAAAECBAgQIEBAgMM7QIAAAQIECBAgQIAAAQIECBAgQIAAAQIECExOQIBjco/MBRMgQIAAAQIECBAgQIAAAQIECBAgQIAAAQICHN4BAgQIECBAgAABAgQIECBAgAABAgQIECBAYHICAhyTe2QumAABAgQIECBAgAABAgQIECBAgAABAgQIEBDg8A4QIECAAAECBAgQIECAAAECBAgQIECAAAECkxMQ4JjcI3PBBAgQIECAAAECBAgQIECAAAECBAgQIECAgACHd4AAAQIECBAgQIAAAQIECBAgQIAAAQIECBCYnIAAx+QemQsmQIAAAQIECBAgQIAAAQIECBAgQIAAAQIEBDi8AwQIECBAgAABAgQIECBAgAABAgQIECBAgMDkBAQ4JvfIXDABAgQIECBAgAABAgQIECBAgAABAgQIECAgwOEdIECAAAECBAgQIECAAAECBAgQIECAAAECBCYnIMAxuUfmggkQIECAAAECBAgQIECAAAECBAgQIECAAAEBDu8AAQIECBAgQIAAAQIECBAgQIAAAQIECBAgMDkBAY7JPTIXTIAAAQIECBAgQIAAAQIECBAgQIAAAQIECAhweAcIECBAgAABAgQIECBAgAABAgQIECBAgACByQn8DpdAxYGIDE9ZAAAAAElFTkSuQmCC"/></switch></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-11"><g><rect x="120" y="0" width="120" height="60" fill="#b1ddf0" style="fill: light-dark(rgb(177, 221, 240), rgb(23, 61, 77)); stroke: light-dark(rgb(16, 115, 158), rgb(84, 169, 206));" stroke="#10739e" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 30px; margin-left: 121px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><div>Camera</div></div></div></div></foreignObject><image x="121" y="23.5" width="118" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-12"><g><rect x="0" y="0" width="120" height="60" fill="#b1ddf0" style="fill: light-dark(rgb(177, 221, 240), rgb(23, 61, 77)); stroke: light-dark(rgb(16, 115, 158), rgb(84, 169, 206));" stroke="#10739e" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 30px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Intake</div></div></div></foreignObject><image x="1" y="23.5" width="118" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-32"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-18"><g><path d="M 35.32 109.94 L 35.4 140" fill="none" stroke="#986a44" style="stroke: light-dark(rgb(152, 106, 68), rgb(252, 185, 158));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-25"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 36px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">1</div></div></div></foreignObject><image x="33" y="119" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAABCklEQVRYR+2VwQ1EQBSGf0UoQROuShEtuIi4iEi0oAsHB6IBFCBxFSUQCbsbyW5WVryYHSdvzs/7Zr73j1Eer4ULl8IAyi4rogyBFclX1HUdqqpaG+u6DlVVDyGnZ2DbNqIoWpvmeQ7DMOQCNE1D27bXAIqi2OxY6gmmaVqd13X9USIN0Pc9TNNEmqYb38KAcRzRNA3KskSWZUiSBMMw/AxTCBCGIRzHwbIsZMaFAK7rwvd9srlwTD3PQxAEu4B5nvH9wgqd4GjrlmUhjmP5KXp3ZACZHFbEinYNnHoyOUWcIk4RwPfgBveAPOJOwam/KQNEDJDf8AxYEWmALFDIij8LGEAKZEU3UPQEa4e4dwht2PoAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-19"><g><path d="M 50.88 109.94 L 50.8 140" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-26"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 51px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="48" y="119" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-23"><g><path d="M 81.6 110.06 L 81.6 140" fill="none" stroke="#0000cc" style="stroke: light-dark(rgb(0, 0, 204), rgb(212, 212, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-29"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 126px; margin-left: 82px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">3</div></div></div></foreignObject><image x="79" y="120" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAAES0lEQVRYR+1XWSh1bRR+DJEMyZR5ukAZMtwRSZkKKSIlEkIpceFCKEkpKSkXXLigZAyJkFkkQ5lFikyZI2PE93/r1d7f2ec43944f3/9nXV1zn7XXs/7Pu9az1pb49dvw79oGmoAMXbVFIkxBDVFP6NoZmYGvb29mJycxPHxMS4vL6GjowNTU1O4u7sjODgYycnJsLS0VAr06R3s7u4iKysLY2NjojvU1dVFTk4OKisrQb/lTQFgbm4OUVFRuLq6Eg0u6+Dn58c2ZGRkJHhPAEBBvby8cHJyInCytrZGeHg47O3tcXt7i/X1ddBG7u/vBX6RkZEYGBhQDkC0NDQ08A56enqora1FWloatLS0BC8eHR0xGuUD9vX1MQY4409wfX0NW1tbPD09sTUNDQ0MDg4iLCxMKVWk9IGBgaBk4CwkJASjo6OKAM3NzUhJSeEXkpKS0NLSInoPKysr8Pb25v0oy2iz+vr6Hxvl+kF2djbq6+t5x+7ubsTGxooCkIOdnR2IMs6Wlpbg6+srBAgICMDs7CzvdH5+DnNzc0kAoaGhGBkZ4X2JWkoKwQlcXV2xs7PDHmpra+P19VVScHJKTExEe3s77z88PAwCFQAUFhbi7u6OPaSCqampkQwQFBSE6elp3n97exsuLi5CAMnR5BzpQq2srPDy8sJWqNCIXq6qfyzX6enpaGxs5GHz8vIEp/82AFVzUVERqLA4I9FbWFhg9cSZJAAKVlVVhYeHB1xcXLBkOD09FZBlYmLCMsnHx0fwXBLA0NAQIiIilF4T0URqamZmpuCjEgBPT0+UlZWxwiSJkTVJAKSc+fn5IO0hag4PD/H+/q6wW1JTUgDZviAJQD4SpeTW1hYqKirQ0dEhWCYQunhOfb8FIBuxv78f0dHR7HScdXZ2Ii4uTjWFRlFyc3NRV1fHA5CET01NqQ6AUtfCwuJP7v++6OfnZzYg/JgiLioBEBBne3t7cHR0/ADo6urCxMQEv5iZmcl681dMXvCoy/n7+38AlJeXo7S0lI9XXV2NgoKCr8RnFby8vMy/s7i4CJo0GEBTUxNSU1P5RWrysgImhkQZZGBggMfHR96VaoU0iQHI91UPDw+sra2JxeXXSf/d3Nz4/6RLdB+ampofALQDZ2dn7O/v805UQPHx8ZJAEhISBAVH/9va2oRpWlxczCqTM8qA1dVVGBoa/hWEqjYmJkbg82nLPDg4AFHDtU16w8HBgU0aXAOXjUJ8l5SUsOYiq0s0EI+Pj/+pCdnPWPnZiPMifmkooD5L4+LGxgY73c3NjWDn1Drn5+f/3nCoS5G2f/X73MnJCT09PQr182kl01yTkZHBvgnEjOSA0po2ZWxsrOCuVCre3t7YjNna2orNzU02cZ+dnYEGYupc9AFCcyhlmo2NjdJ9qEyLlCGoAcRyQHX9QH0Holz/jyn69tklviicVCW+9BU3NYAoW2qK/nuK/gGxK3KGRMumSQAAAABJRU5ErkJggg=="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-24"><g><path d="M 66.2 110.12 L 66.2 140" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-28"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 126px; margin-left: 67px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="64" y="120" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-17"><g><rect x="20" y="80" width="77" height="30" fill="#eeeeee" style="fill: light-dark(rgb(238, 238, 238), rgb(32, 32, 32)); stroke: light-dark(rgb(54, 57, 61), rgb(186, 189, 192));" stroke="#36393d" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 75px; height: 1px; padding-top: 95px; margin-left: 21px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Sensor<br />Intake</div></div></div></foreignObject><image x="21" y="81" width="75" height="32" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAACACAYAAACx1FRUAAAaRUlEQVR4Xu2dBbgsSZGFB3dYGFzfMLi7yxsGd3e5MLjLsoszuCzOIou+wd1dL+7u/nCXxR3OD50QJCVZWdU9t7pPfF98t6orrU5WnRsZGZl1mH0sRsAIGIGZIHCYmbTTzTQCRsAI7GPC8kNgBIzAbBAwYc2mq9xQI2AETFh+BoyAEZgNAias2XSVG2oEjIAJy8+AETACs0HAhDWbrnJDjYARMGH5GTACRmA2CJiwZtNVbqgRMAImLD8DRsAIzAYBE9b0XXV+Fblbej7paaXHlv6H9JfS7wZ9m45fLv3/6ZvgEo3AeiJgwpqmX8HxJtI7Ss80oMjfKe3rpI+QvndAPic1AhuJgAlrfLefQkUcIr3oiKL+pLz3lT5E+ucR5TirEVhrBExY47r35Mr+Lil/2+R7uvAN6b6LdEfoSPtmXbu89PfjmuXcRmA9ETBh1ffrEZX149LTZ0VgLT1P+jjpZ6W/DtcPq+MTS88qvbH0qtK8Dx6s3+5Z3yznNALri4AJq75v/1tZH5pl/+SChL5SWOyFlO4F0pOE9BDehaXvKyzDyYzAxiBgwqrraiwlhnrHC9k5P4/0mwOLZCbx/VJmEpN8aFHWwKKc3AisNwImrLr+xQJ6Z5b1Sjp/VV1xf5tdfHSWl6Hm5yvLczYjsJYImLDquvW/lO1hISsxVsRb/bGuuH2OqXw/lOIXS/KfOnhkZXnOZgTWEgETVl23Yg1hFSX5gg5OV1fUP3LhsyLYNMljszpGFu/sRmD+CJiw6vrwqcp205D15zo+Vl1R/8hFLNcVQxnP1PGdB5RJuMSB0qtLzy5lNhK/GCEVTAJ8SfocKf6xKeVEKuxm0vNKz7zA4Yv6i1LX/0l/U1EhM6nXlELip5FiwXKPP5V+S/pB6aulb5TWxq4RakIYCbjjSzyh9OhSrN3vS/kn8hopqxL+UHEPTVkOrx95dm4kZdj/K+kNpW+dqPy1LsaEVde991C2B2VZeVk/XVfc6Fy8cFhkuwpKeofSEDrxpoK0JGES4aSLtB/W33Mvjo+6qJMXryu2DHK5t3RPYX1nULonSksDcbFuD5K+p7B8kh1Zyj8D+vFoBfkgfIboryhISxKIFCJMkt4zCBGCPUtWzi11DrFbehAwYdU9ItdQthdlWbEmLiid6j9xScsOp0RYe8R0DZG/KDF+OJYE9UkTYUFgTDBgyZXKrZXwST2Jr6DrL5YeqbTQRTpCQegT1mb2CTO7kMaQtqcyn6KDW0n7LLomwjqB8m1Lm1wHJqy+XltcN2EVApUlO5nOvyyNTnKSfEJ6C+kH6oodlIvQCgJUr9WQi2HG56Q/k/KiYLVAbrn8j36AuLokJ6wDlBhyTi8egbEMZ74qxeqirnNJc9L5rX5jeAdGTXI2/QhuOaaQw7elX19k2qW/xK3lzy7lM8Ta23Ezx9E1LLEm0viFfmcYy5CTYS5pmjB7ln7HquySnLDoKzACuyYxYfUAmi6bsAqBakj2GP12h4bfsV4Ydj1f+lLpj+ur6MzZFAoBuTDMod7oN8Kq4KW4u/QoWan4vEjfJpGwPqJEDMGuK4VImMW8v5RZ0iin0gnDuktkv/MiRz9dvAyRXCD8QPlgjOaxbfibHiDFqoqyRydd1uZzF22PefDxQdoM91iMngSiv7mUVQc5+XL/9G+b5IR1VyXkn0MSrPCPSVkpwW4dWJVT+xY7mjffSyas+r47rrIS2c5/4zZhqMKDyBpBlADRKYaMp1Q5n5Ji0SRhtwf8JlgIbcIwiOFQDHjFMsIyaVu/GAmLtuOvgpS3pFgbbUI6CA7fXhKsINpM/ig41SHCKLfRCaTXJjy7OMQvGxJw78eXNoWXXEq/vyErjP7gdyZN2uScuoDTndCTJD/Swf4d+XLCggghPe4bX+PDpWw1ZBmIgAlrIGBZcoYmr5TyUJcIw45taSKw2sBQXmR8KUl44bA6iLbvk6spwUuyRF0xX5GwUjZ8RayD7JOLKUE++wWhMAsXZUsnzIom4T66/hGkdOw9lm/Lc2r9xnA9F8gKckrCLCDD15/03YSuQ4qvzdKB/5Nb8uaERTLI6jrSFxbU5yQtCJiwxj8aDLGYBWOmipdxiEAGWAk4o7GYSuQYSoRPh79J7qQDhk6lwg4TrGNMQt35zFW6lhMWL945pAxn+oQZOEg6Pme7dJ78USl/vi4TQr9kX+G6zmwf+4nF8m+r889keffTOTN9MR399YyCOlIS+ulyIT1DOnBokibCeoIS0jbLCARMWCPAy7IyBMI/cz0plsXQuCyW+hA9zwvYJdfWxeg/IWyAF3JIlD0hA9tZJUy5Y3XkkhMW7Ysvbh+CkFPcfmeXznPCwom9JxT0HR2TborhM8Xiv4szlExGcL/RZ9V3H5dRgrxv8HP9oCFjTli4BhjG4y+zjEDAhDUCvI6szC4Rr4TTGWV2rCtWKRZ1P52guZ8npWHbmtuFDAwxILEhQvAi/h6CJJPQzrc0FJITFm07eEBlBKzihE+ySwc5YRF0ij8pyiE6gWjwe40VhptboRCc79cfWCiY4buK/4gg7qZ/MDlhMWwl5MUyEgET1kgAC7NDDFg1F5dCDGfsyUe4ApZakxDhnYI3uX4X6aMK2xGT4RCPQ5q2OKmcsLCGupzteVNKCAtHPPUQdhCF3xjq4ics3bKnCQomR6LzfyjppjK3F/2Yzu+jA2Yrc8kJy8usKh7QpiwmrImAHFgMjmGsIpyw+QaAqSiuNzloCZOILzaEBfkMFUIS4mQBWzQTopBLTlj4voZElZcQFnWyvIfAzDahHIbN75bigxtCYMzIMQRMwv770clfil0+2fG/yhit3VROTliEoEBalpEImLBGAjhBduKGiDjPrQuGTQQvxiER/YVfpymgcWxT2qyAnLDaZuG6iKZvSJjy8mIz5V8yfMZ393op1ihxb21DaMomJg0HfZIDdUCowlB5oDLE3WBZm3mDhkJywtpSGoa4lpEImLBGAjhRdqbwWafHguUouZWF/wSH8TIEpzTDwlxywoJ8hlg3pRZWqheLE2uPsIkS4iLfR6UQP0O/XPA95c57vmyUzySWYHo3JeJDIUnaAmFzwmqzlkvqdJqAgAlr+ONANHaMEWI5R2lIQldtu3Xx7VkC/CP4SZIsk7CerkriDhSpzlUTVqqXwFyi8C8tPUAaAzebcISUiCdjUiIXrNQYrc4sbo51yZOA7yv2B+tJm5ZGmbBK0KxIY8IaDhpDEHxPSTD1t4YX05iDYU7c350lM7y0SZqGhHxmbJnT5YcWYUWAGAKz1vAiC2UCg+1mciF8gH8oTExEyX1YWGN7KvqMYfPtQz52WGAmMxcTVgW4JVlMWCUo/Wua3PGKAzoGYQ4v8Z85iKaOS01wMufbrBAljvWRhBe5bUHxmLakvDuBsPL7YKiIlcQQbXd2kQkIFl9HwQKOH7g9WOdYS0OFZU0xoLXtC0cmrKHIFqY3YRUCFZIRVR7DCIjNIYCwb8uRkpryCPSmeCGGMvElZZM7Fs8uS3YiYcV7JYI8+t7oB8JI4uJvwjCic7zNWd6FITsuECS6b0jE4ut8mROXTVhLehpNWMOBbfoAxVVUTOnmbm018pIxdInBnE3xQmwcyI4MSWoCR4l7upc09T8zbPhmmqLll01Y+Kbi/vjcF23J1xu24cauqvmCbxZ5x6VDEBrEloT1g/ghh3ywlpnFPLCWfcFYJpWLCautt0b+bsIaDiCzTrzEMa6HWTOGZvk2K0NKJ54ndxizTXA+84UDOk7J47dh4fOQmTtit+LmfV3r4pZNWAzvWCITn0WGxYQslAj+LdYrxm1ziCqPi6KZ2WRyJNaxpfMhoQYs+L5yaBAfyW0LADZhlfRcRRoTVgVoysLsHRZKFLYcxtKKX3ouLZ3YJnwvcUEz2y3H6OxYFi9LDDhleQjrGCGvPsH/xZR+XKjdFjRKWcsmLOog5iyuN3yazgkkLRGi/nMnO2XR7ihYR1hJSbBm2a2hJEyEmcqcQPkHQ+Bok5iwSnquIo0JqwI0ZWHYxtYwcUaPktgxk1mjkp0MUs27dcDXn/GDRWGxbb5/U7pO+AFbI0chLIE9pLoW9DJ8wgeGNZgEgmWmEV9ck6yCsCCog0LlEC+4ENXeJfQD29fwAdsk7LQKEeXCXmEQSRTKh4zYobVNWL6ERRvXEDKk3E/ato+WCaun42ovm7Bqkfv7V2LYBiVaRZSGP4gHFqsHJzrWQ3whGLqwYR35cdqyvjAXCCyGTuTX6TcsBmbKojB8xFpitjEGSzKM5cssWIW8aFGwZCCMNlkFYTX5BSFefFsQcR62wTCSfb3YQTXfFqfL8sHfxyRFFDYwJH6L7WMiZliibHcNZjFKnrx9gaAmrI4HaswlE9YY9P4eF8RQIe782VQiLwL+Lcgqf/jz9BARH2Po26UAhy+foUpftInlsO0uEeZYTVzff1F3XtdD9QMvfZesgrCoP/cRxTbho8IHxTpK7geLsOlrN1hMhIG0zdhCQvi2GILnguMeqxns8E8SBgHR54Jly9bJXWLC6gGo9rIJqxa5f+Yj5ocZqDgsqSmVl4yXgbCJ0u/48TEMfGc1H3ElZIKp/q41eNzHqgiL2UKGXnFB9hAcsWYZ9nVtd0x5DL3BrG3Dwq46mRRhvWMfZiasIT03IK0JawBYHUnBkW1XsFYY7g0Rhj484Ax/WE84VPDj8BEFZv76LD3KZhqedj67sKJVERbNwQIlbANfXP71nLbmYkWS5/HSkkmHVA8fhmCn0xLMsO7Al2FjiZiwSlCqSGPCqgCtJwtT6GzstltKrA8ffGBGjrVsDDdQSIAFu8wMMqTs+nBEaQupBwsDZQYRSwL/GrNgzIixQR5OfF6mIbuTltY/ZTqGbvjwCE8gtIN7wwKj3dwPS5ggdxzu3M+QeKrYTuph+I1ipYIZ/wAgQfaVZ8jN9xex/HY6ZlPiv2PLMmHt2K5xw4yAEcgRMGH5mTACRmA2CJiwZtNVbqgRMAImLD8DRsAIzAYBE9ZsusoNNQJGwITlZ8AIGIHZIGDCmk1XuaFGwAiYsPwMGAEjMBsETFiz6So31AgYAROWnwEjYARmg4AJazZd5YYaASNgwvIzYASMwGwQMGHNpqvcUCNgBExYfgaMgBGYDQImrNl0lRtqBIyACcvPgBEwArNBwIQ1m65yQ42AETBh+RkwAkZgNgiYsGbTVW6oETACJiw/A0bACMwGARPWbLrKDTUCRsCEtZpngE9EPSKris/G8wUdSzsCfIaLj70meaAO7m3ANhcBE9Zq+t6EVYezCasOt7XNZcJaTdeasOpwNmHV4ba2uUxYq+nanUxYpxUE2wGGz+j44quBpbcWE1YvRJuVwIS1mv7eyYR1RkHw6QDDx3V89tXA0luLCasXos1KYMJaTX+bsOpwNmHV4ba2uUxYq+laE1YdziasOtzWNpcJazVda8Kqw9mEVYfb2uYyYa2ma01YdTibsOpwW9tcJqzVdO3UhHVUNfsG0gOlZ5aeXLpX+kXpZ6VPkH6n8NamcLofVnVdQnox6YWkJ5buKz2K9OfSH0uZffyA9MXSrxS27dAiLIJ6ryC9ivQ00hNJjyj9qvTLUiYpnib9ZuF9ONlECJiwJgKyp5ihhPVslXf9UObRdfyrxfmd9Pde0uN01PlrXXuUlCjxlC8mf7xOblt46z9TumN3pKWdRJ/zYpfKC5UQTL7dk6GWsK6tcp8jPVwof1vHl2/BIyU7gg7uKL2v9Gg9bfuDrr9A+mDp50tv3OnGIWDCGodfae4pCOuPqoz/6pHI+urHmrlmQ6IpCIuX+4nSm/Y1ouX6N/Q7FlmXtVVDWNdRmRB+JKu36RyLCSJvk+Pqwhuk5xx4P79Q+itJ3z4wn5NXIGDCqgCtIstYwjqG6nxSIKs/6/g9UoZZ/Kc/3eJFa7K6bqFrT8naPAVhYeU9oAEL2oPl9C3pn6QMD08qZXiYy3v1A0PIv7RgOpSwrqdyDpFGsnqLzq8o/U1Hvx1P1yAchse5/EA/fEn6eylD7/0b0vxOv11X+rKOOnxpAgRMWBOAWFDEWMK6s+pgiIe8Vspwbm9W7zF1DoHcThr79ac6P74UCy0J0e2QCLKf9KnhGhYPJJeEfO/I6iIvw6A4bGLo+DDpk6UcR4GsriVl6HjK7NqFdf7u7Ld0OoSwsDz3SCNZvUnnWD+/bSk//fyKRbqYDDK9p/SdUv5BJAE7fseHGAVC5Jr9Wj1gj7lswhqDXnnesYSF1cIQ7BlShmBtFgktwhK7Zda08+v8/S3NrXG6Q4xYWEkgNZzu2z2QnGLRjhOGdJR1n5Z8pYQFeeyR4vxPwvAOp3kfWeHven5WP5MWt5dGosqbeCP98HRpJEj8ZjmR9UDiy0MQMGENQas+7VjCoubvSrFO+l5Aho/fkzKTmAQ/Fv6sJqkhrDeroLjekCFntMq6kGKbHfBIAgkf1JKhhLBuqLzPlEayep3OryplqNYnDKvPEBIxkwnBd/1TSMnzoTV5ziP9cF+lvl6HgAmrDrehuaYgrDuo0scVVsxLx4uTZEsH+HaapIaw8pcccnh5Ydvw9Tw3pGWmDUd5k/QRFveFlRPJ6jU6v5oUn1OfXFQJtkMiLCpw+0hfxsV1HPWEORwrpIfEsM4sS0DAhLUEUBuKHEtYxDHhN+qzrlLVkBOWR5ItHUxJWIQwMERNwktbYs2QfirCurHKYtY0ktUrdY41WUJWtAV/W7QMu8iz7UkhBOLgcBHf3ulX81htXi0mrNX0+VjCwum9e0BTcaLHcIOpCWtAU/4tKVYiEwNJaiysmyzIKj6/H9RvzDji7yuVTyohgbdJbqUDSGyInFeJo3+QmVHi5kr/uQypa+PTmrBW8wiMJSx8NLykpbITCYvwBiYDmGGLVtFQwvq68uMzy59dZunOJCUavUTw9TGbGdtCDNZHSzKHNJRDNH8UfGKfG1iOkxcgYMIqAGmCJGMJixm5Bw1ox6FFWAwTiVNiyMhfQiZOJcWKSWEU+W0MIazkO2t7bpkMuGQhTrQJCysJ/qtLS0uHkykfbSHWK84W4hsjHMIyMQImrIkBbSluLGHdTOXirymVVRIWTvu0rvGsOo6+rZL2DiGsWB4E8z7pBbNKiMeKTv22NlxEF/L4spL2lqQhnILYLsvECJiwJgZ0SYSFP4rZsFJZBWERS8U6ui1p6XNEuAVWEou2k9QQFn4i4qBeJWWxd7TefqhzIv9/0gMWAaXLIhWCZF9U2llOV45A6YNWXqJTNiEw1sLaaYTFEhWi00/W0t0EkjJbxuwhO0iwuwFbL/OXEIiXjCAsysaKYgE10kQ8XbFdqeplEhYWJ0GklokRMGFNDGhLcetEWIfXPX5KihUThTV3RIhvSwmcbFtoTIxULWFBVsRsxfy0gRiwK4fGEMB5gLRryMeSoOhn+prO82VDq3k6XEsxAiasYqhGJVwnwoIwnpehQbDk3TpIKiYfQ1iPVkGsq8yFISFDQ2bsknxBB/jU2uLD8oDZvm10Rj0AzjwNAiasaXDsK2WdCOv1ullm05K8UQeXkZYsZSHP1aVxmdAQH1bXl5+JLn9s1hEH6/x+LZ1zJP3Ol7f5m4RF4vjALDsUARPWajpmnQgLn9SpA2xbOm6Lom9CNyeWqQiLeCqWJJ0rVIp1hZWFtdUkbNFzgXChJnAUSy3uUUac2NDg09U8hWtQiwlrNZ24ToTFDqZxYXXX9jBN6LIzAjskJJmKsCjvHFIi3mNMFH4s/FlNFiDWV9wpgq112CKGWchSebUSspNpkrZha2l5TteBgAlrNY/HOhEWu0bE7WHYaYFZuRLZrUR5kOWUhEUbHinN/VxtbcT3hbOdiYQk99DBQ0puRmmaYrkcNFoIXk0yE1YNasPzzImwcF437byZ7pqdOSGeJO/SARZMn1VCGAFbF0fHOGW0bePMtb7dGpp6gk0FuQdCL5IQk8WsZpN/ivADdipNgiXGgui4qWFTPWfTj2DBByuSfEgHcZeMpnz+bQQCJqwR4A3IOifC+qXui3V/7FXeJHwEI+1+mq4/Swd3lRLakAv+LpYWxd0jYhr8S6y9a9osr4awKJv92wkqjUL0e/Q1pWs42gnT4G8UgkrZQTXf+JAPcoAB2/2wy2sScGN4TLyZZUkImLCWBGxW7E4mLPYzz4mGF5jZP5zWWES8nEnYiYA9zuOwkGv4tvAXMcTi5aXcc0vjbgikw4eV739FXcRS7V3Um+qqJSzyE6tFCEWUS+mEbZNz4Xe2pokzhikNn0vjnlhczdpIdk2NQ0jSYV1iQbJ9tWWJCJiwlghuKHonExbN5As2bVHrTfFJWBK8+EceAB/brdxcyrAw94OlYiCZa4QyxxAWViI7JkQriJ0c2NGh6YMUfMEHqyofsvbdImTFdjlsTW1ZMgImrCUDvCh+pxPW5dROZruanoe2gEqGccyIleyOgJ+KwNK09UtTzBRQTUlYlMfHOghqjcK3Gu/e0u1YTzjcmcUseTeI6L+NlJlJywoQKOmUFTTDVewABNgLClLBmYy1heXwI+knpHwmq00uqwvsIno+KX4ghlXfl/L1GIZ6L5Wy4DkKzx0zd7zsbENDGMJeKY7ux3TUtapLbI/DEI+hIiR2AilffsZ5z/CQ+C0+6dX2YY9VtXPj6jFhbVyX+4aNwHwRMGHNt+/cciOwcQiYsDauy33DRmC+CJiw5tt3brkR2DgETFgb1+W+YSMwXwRMWPPtO7fcCGwcAiasjety37ARmC8CJqz59p1bbgQ2DgET1sZ1uW/YCMwXARPWfPvOLTcCG4eACWvjutw3bATmi4AJa75955YbgY1DwIS1cV3uGzYC80XAhDXfvnPLjcDGIWDC2rgu9w0bgfkiYMKab9+55UZg4xAwYW1cl/uGjcB8ETBhzbfv3HIjsHEImLA2rst9w0ZgvgiYsObbd265Edg4BExYG9flvmEjMF8ETFjz7Tu33AhsHAImrI3rct+wEZgvAn8FcTk1zMTOct8AAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-33"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-34"><g><path d="M 155.32 109.94 L 155.4 140" fill="none" stroke="#986a44" style="stroke: light-dark(rgb(152, 106, 68), rgb(252, 185, 158));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-35"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 156px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">1</div></div></div></foreignObject><image x="153" y="119" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAABCklEQVRYR+2VwQ1EQBSGf0UoQROuShEtuIi4iEi0oAsHB6IBFCBxFSUQCbsbyW5WVryYHSdvzs/7Zr73j1Eer4ULl8IAyi4rogyBFclX1HUdqqpaG+u6DlVVDyGnZ2DbNqIoWpvmeQ7DMOQCNE1D27bXAIqi2OxY6gmmaVqd13X9USIN0Pc9TNNEmqYb38KAcRzRNA3KskSWZUiSBMMw/AxTCBCGIRzHwbIsZMaFAK7rwvd9srlwTD3PQxAEu4B5nvH9wgqd4GjrlmUhjmP5KXp3ZACZHFbEinYNnHoyOUWcIk4RwPfgBveAPOJOwam/KQNEDJDf8AxYEWmALFDIij8LGEAKZEU3UPQEa4e4dwht2PoAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-36"><g><path d="M 170.88 109.94 L 170.8 140" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-37"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 171px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="168" y="119" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-38"><g><path d="M 201.6 110.06 L 201.6 140" fill="none" stroke="#0000cc" style="stroke: light-dark(rgb(0, 0, 204), rgb(212, 212, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-39"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 126px; margin-left: 202px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">3</div></div></div></foreignObject><image x="199" y="120" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAAES0lEQVRYR+1XWSh1bRR+DJEMyZR5ukAZMtwRSZkKKSIlEkIpceFCKEkpKSkXXLigZAyJkFkkQ5lFikyZI2PE93/r1d7f2ec43944f3/9nXV1zn7XXs/7Pu9az1pb49dvw79oGmoAMXbVFIkxBDVFP6NoZmYGvb29mJycxPHxMS4vL6GjowNTU1O4u7sjODgYycnJsLS0VAr06R3s7u4iKysLY2NjojvU1dVFTk4OKisrQb/lTQFgbm4OUVFRuLq6Eg0u6+Dn58c2ZGRkJHhPAEBBvby8cHJyInCytrZGeHg47O3tcXt7i/X1ddBG7u/vBX6RkZEYGBhQDkC0NDQ08A56enqora1FWloatLS0BC8eHR0xGuUD9vX1MQY4409wfX0NW1tbPD09sTUNDQ0MDg4iLCxMKVWk9IGBgaBk4CwkJASjo6OKAM3NzUhJSeEXkpKS0NLSInoPKysr8Pb25v0oy2iz+vr6Hxvl+kF2djbq6+t5x+7ubsTGxooCkIOdnR2IMs6Wlpbg6+srBAgICMDs7CzvdH5+DnNzc0kAoaGhGBkZ4X2JWkoKwQlcXV2xs7PDHmpra+P19VVScHJKTExEe3s77z88PAwCFQAUFhbi7u6OPaSCqampkQwQFBSE6elp3n97exsuLi5CAMnR5BzpQq2srPDy8sJWqNCIXq6qfyzX6enpaGxs5GHz8vIEp/82AFVzUVERqLA4I9FbWFhg9cSZJAAKVlVVhYeHB1xcXLBkOD09FZBlYmLCMsnHx0fwXBLA0NAQIiIilF4T0URqamZmpuCjEgBPT0+UlZWxwiSJkTVJAKSc+fn5IO0hag4PD/H+/q6wW1JTUgDZviAJQD4SpeTW1hYqKirQ0dEhWCYQunhOfb8FIBuxv78f0dHR7HScdXZ2Ii4uTjWFRlFyc3NRV1fHA5CET01NqQ6AUtfCwuJP7v++6OfnZzYg/JgiLioBEBBne3t7cHR0/ADo6urCxMQEv5iZmcl681dMXvCoy/n7+38AlJeXo7S0lI9XXV2NgoKCr8RnFby8vMy/s7i4CJo0GEBTUxNSU1P5RWrysgImhkQZZGBggMfHR96VaoU0iQHI91UPDw+sra2JxeXXSf/d3Nz4/6RLdB+ampofALQDZ2dn7O/v805UQPHx8ZJAEhISBAVH/9va2oRpWlxczCqTM8qA1dVVGBoa/hWEqjYmJkbg82nLPDg4AFHDtU16w8HBgU0aXAOXjUJ8l5SUsOYiq0s0EI+Pj/+pCdnPWPnZiPMifmkooD5L4+LGxgY73c3NjWDn1Drn5+f/3nCoS5G2f/X73MnJCT09PQr182kl01yTkZHBvgnEjOSA0po2ZWxsrOCuVCre3t7YjNna2orNzU02cZ+dnYEGYupc9AFCcyhlmo2NjdJ9qEyLlCGoAcRyQHX9QH0Holz/jyn69tklviicVCW+9BU3NYAoW2qK/nuK/gGxK3KGRMumSQAAAABJRU5ErkJggg=="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-40"><g><path d="M 186.2 110.12 L 186.2 140" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-41"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 126px; margin-left: 187px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="184" y="120" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-42"><g><rect x="140" y="80" width="77" height="30" fill="#eeeeee" style="fill: light-dark(rgb(238, 238, 238), rgb(32, 32, 32)); stroke: light-dark(rgb(54, 57, 61), rgb(186, 189, 192));" stroke="#36393d" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 75px; height: 1px; padding-top: 95px; margin-left: 141px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Sensor<br />Camera</div></div></div></foreignObject><image x="141" y="81" width="75" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-43"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-44"><g><path d="M 578.32 119.94 L 578.4 150" fill="none" stroke="#986a44" style="stroke: light-dark(rgb(152, 106, 68), rgb(252, 185, 158));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-45"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 579px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">1</div></div></div></foreignObject><image x="576" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAABCklEQVRYR+2VwQ1EQBSGf0UoQROuShEtuIi4iEi0oAsHB6IBFCBxFSUQCbsbyW5WVryYHSdvzs/7Zr73j1Eer4ULl8IAyi4rogyBFclX1HUdqqpaG+u6DlVVDyGnZ2DbNqIoWpvmeQ7DMOQCNE1D27bXAIqi2OxY6gmmaVqd13X9USIN0Pc9TNNEmqYb38KAcRzRNA3KskSWZUiSBMMw/AxTCBCGIRzHwbIsZMaFAK7rwvd9srlwTD3PQxAEu4B5nvH9wgqd4GjrlmUhjmP5KXp3ZACZHFbEinYNnHoyOUWcIk4RwPfgBveAPOJOwam/KQNEDJDf8AxYEWmALFDIij8LGEAKZEU3UPQEa4e4dwht2PoAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-46"><g><path d="M 593.88 119.94 L 593.8 150" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-47"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 594px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="591" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-48"><g><path d="M 624.6 120.06 L 624.6 150" fill="none" stroke="#0000cc" style="stroke: light-dark(rgb(0, 0, 204), rgb(212, 212, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-49"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 625px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">3</div></div></div></foreignObject><image x="622" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAAES0lEQVRYR+1XWSh1bRR+DJEMyZR5ukAZMtwRSZkKKSIlEkIpceFCKEkpKSkXXLigZAyJkFkkQ5lFikyZI2PE93/r1d7f2ec43944f3/9nXV1zn7XXs/7Pu9az1pb49dvw79oGmoAMXbVFIkxBDVFP6NoZmYGvb29mJycxPHxMS4vL6GjowNTU1O4u7sjODgYycnJsLS0VAr06R3s7u4iKysLY2NjojvU1dVFTk4OKisrQb/lTQFgbm4OUVFRuLq6Eg0u6+Dn58c2ZGRkJHhPAEBBvby8cHJyInCytrZGeHg47O3tcXt7i/X1ddBG7u/vBX6RkZEYGBhQDkC0NDQ08A56enqora1FWloatLS0BC8eHR0xGuUD9vX1MQY4409wfX0NW1tbPD09sTUNDQ0MDg4iLCxMKVWk9IGBgaBk4CwkJASjo6OKAM3NzUhJSeEXkpKS0NLSInoPKysr8Pb25v0oy2iz+vr6Hxvl+kF2djbq6+t5x+7ubsTGxooCkIOdnR2IMs6Wlpbg6+srBAgICMDs7CzvdH5+DnNzc0kAoaGhGBkZ4X2JWkoKwQlcXV2xs7PDHmpra+P19VVScHJKTExEe3s77z88PAwCFQAUFhbi7u6OPaSCqampkQwQFBSE6elp3n97exsuLi5CAMnR5BzpQq2srPDy8sJWqNCIXq6qfyzX6enpaGxs5GHz8vIEp/82AFVzUVERqLA4I9FbWFhg9cSZJAAKVlVVhYeHB1xcXLBkOD09FZBlYmLCMsnHx0fwXBLA0NAQIiIilF4T0URqamZmpuCjEgBPT0+UlZWxwiSJkTVJAKSc+fn5IO0hag4PD/H+/q6wW1JTUgDZviAJQD4SpeTW1hYqKirQ0dEhWCYQunhOfb8FIBuxv78f0dHR7HScdXZ2Ii4uTjWFRlFyc3NRV1fHA5CET01NqQ6AUtfCwuJP7v++6OfnZzYg/JgiLioBEBBne3t7cHR0/ADo6urCxMQEv5iZmcl681dMXvCoy/n7+38AlJeXo7S0lI9XXV2NgoKCr8RnFby8vMy/s7i4CJo0GEBTUxNSU1P5RWrysgImhkQZZGBggMfHR96VaoU0iQHI91UPDw+sra2JxeXXSf/d3Nz4/6RLdB+ampofALQDZ2dn7O/v805UQPHx8ZJAEhISBAVH/9va2oRpWlxczCqTM8qA1dVVGBoa/hWEqjYmJkbg82nLPDg4AFHDtU16w8HBgU0aXAOXjUJ8l5SUsOYiq0s0EI+Pj/+pCdnPWPnZiPMifmkooD5L4+LGxgY73c3NjWDn1Drn5+f/3nCoS5G2f/X73MnJCT09PQr182kl01yTkZHBvgnEjOSA0po2ZWxsrOCuVCre3t7YjNna2orNzU02cZ+dnYEGYupc9AFCcyhlmo2NjdJ9qEyLlCGoAcRyQHX9QH0Holz/jyn69tklviicVCW+9BU3NYAoW2qK/nuK/gGxK3KGRMumSQAAAABJRU5ErkJggg=="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-50"><g><path d="M 609.2 120.12 L 609.2 150" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-51"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 610px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="607" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-52"><g><rect x="563" y="90" width="77" height="30" fill="#eeeeee" style="fill: light-dark(rgb(238, 238, 238), rgb(32, 32, 32)); stroke: light-dark(rgb(54, 57, 61), rgb(186, 189, 192));" stroke="#36393d" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 75px; height: 1px; padding-top: 105px; margin-left: 564px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Sensor N</div></div></div></foreignObject><image x="564" y="98.5" width="75" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-53"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-54"><g><path d="M 698.32 119.94 L 698.4 150" fill="none" stroke="#986a44" style="stroke: light-dark(rgb(152, 106, 68), rgb(252, 185, 158));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-55"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 699px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">1</div></div></div></foreignObject><image x="696" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAABCklEQVRYR+2VwQ1EQBSGf0UoQROuShEtuIi4iEi0oAsHB6IBFCBxFSUQCbsbyW5WVryYHSdvzs/7Zr73j1Eer4ULl8IAyi4rogyBFclX1HUdqqpaG+u6DlVVDyGnZ2DbNqIoWpvmeQ7DMOQCNE1D27bXAIqi2OxY6gmmaVqd13X9USIN0Pc9TNNEmqYb38KAcRzRNA3KskSWZUiSBMMw/AxTCBCGIRzHwbIsZMaFAK7rwvd9srlwTD3PQxAEu4B5nvH9wgqd4GjrlmUhjmP5KXp3ZACZHFbEinYNnHoyOUWcIk4RwPfgBveAPOJOwam/KQNEDJDf8AxYEWmALFDIij8LGEAKZEU3UPQEa4e4dwht2PoAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-56"><g><path d="M 713.88 119.94 L 713.8 150" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-57"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 714px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="711" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-58"><g><path d="M 744.6 120.06 L 744.6 150" fill="none" stroke="#0000cc" style="stroke: light-dark(rgb(0, 0, 204), rgb(212, 212, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-59"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 745px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">3</div></div></div></foreignObject><image x="742" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAAES0lEQVRYR+1XWSh1bRR+DJEMyZR5ukAZMtwRSZkKKSIlEkIpceFCKEkpKSkXXLigZAyJkFkkQ5lFikyZI2PE93/r1d7f2ec43944f3/9nXV1zn7XXs/7Pu9az1pb49dvw79oGmoAMXbVFIkxBDVFP6NoZmYGvb29mJycxPHxMS4vL6GjowNTU1O4u7sjODgYycnJsLS0VAr06R3s7u4iKysLY2NjojvU1dVFTk4OKisrQb/lTQFgbm4OUVFRuLq6Eg0u6+Dn58c2ZGRkJHhPAEBBvby8cHJyInCytrZGeHg47O3tcXt7i/X1ddBG7u/vBX6RkZEYGBhQDkC0NDQ08A56enqora1FWloatLS0BC8eHR0xGuUD9vX1MQY4409wfX0NW1tbPD09sTUNDQ0MDg4iLCxMKVWk9IGBgaBk4CwkJASjo6OKAM3NzUhJSeEXkpKS0NLSInoPKysr8Pb25v0oy2iz+vr6Hxvl+kF2djbq6+t5x+7ubsTGxooCkIOdnR2IMs6Wlpbg6+srBAgICMDs7CzvdH5+DnNzc0kAoaGhGBkZ4X2JWkoKwQlcXV2xs7PDHmpra+P19VVScHJKTExEe3s77z88PAwCFQAUFhbi7u6OPaSCqampkQwQFBSE6elp3n97exsuLi5CAMnR5BzpQq2srPDy8sJWqNCIXq6qfyzX6enpaGxs5GHz8vIEp/82AFVzUVERqLA4I9FbWFhg9cSZJAAKVlVVhYeHB1xcXLBkOD09FZBlYmLCMsnHx0fwXBLA0NAQIiIilF4T0URqamZmpuCjEgBPT0+UlZWxwiSJkTVJAKSc+fn5IO0hag4PD/H+/q6wW1JTUgDZviAJQD4SpeTW1hYqKirQ0dEhWCYQunhOfb8FIBuxv78f0dHR7HScdXZ2Ii4uTjWFRlFyc3NRV1fHA5CET01NqQ6AUtfCwuJP7v++6OfnZzYg/JgiLioBEBBne3t7cHR0/ADo6urCxMQEv5iZmcl681dMXvCoy/n7+38AlJeXo7S0lI9XXV2NgoKCr8RnFby8vMy/s7i4CJo0GEBTUxNSU1P5RWrysgImhkQZZGBggMfHR96VaoU0iQHI91UPDw+sra2JxeXXSf/d3Nz4/6RLdB+ampofALQDZ2dn7O/v805UQPHx8ZJAEhISBAVH/9va2oRpWlxczCqTM8qA1dVVGBoa/hWEqjYmJkbg82nLPDg4AFHDtU16w8HBgU0aXAOXjUJ8l5SUsOYiq0s0EI+Pj/+pCdnPWPnZiPMifmkooD5L4+LGxgY73c3NjWDn1Drn5+f/3nCoS5G2f/X73MnJCT09PQr182kl01yTkZHBvgnEjOSA0po2ZWxsrOCuVCre3t7YjNna2orNzU02cZ+dnYEGYupc9AFCcyhlmo2NjdJ9qEyLlCGoAcRyQHX9QH0Holz/jyn69tklviicVCW+9BU3NYAoW2qK/nuK/gGxK3KGRMumSQAAAABJRU5ErkJggg=="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-60"><g><path d="M 729.2 120.12 L 729.2 150" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-61"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 730px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="727" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-62"><g><rect x="683" y="90" width="77" height="30" fill="#eeeeee" style="fill: light-dark(rgb(238, 238, 238), rgb(32, 32, 32)); stroke: light-dark(rgb(54, 57, 61), rgb(186, 189, 192));" stroke="#36393d" pointer-events="all"/></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-63"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-64"><g><path d="M 818.32 119.94 L 818.4 150" fill="none" stroke="#986a44" style="stroke: light-dark(rgb(152, 106, 68), rgb(252, 185, 158));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-65"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 819px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">1</div></div></div></foreignObject><image x="816" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAABCklEQVRYR+2VwQ1EQBSGf0UoQROuShEtuIi4iEi0oAsHB6IBFCBxFSUQCbsbyW5WVryYHSdvzs/7Zr73j1Eer4ULl8IAyi4rogyBFclX1HUdqqpaG+u6DlVVDyGnZ2DbNqIoWpvmeQ7DMOQCNE1D27bXAIqi2OxY6gmmaVqd13X9USIN0Pc9TNNEmqYb38KAcRzRNA3KskSWZUiSBMMw/AxTCBCGIRzHwbIsZMaFAK7rwvd9srlwTD3PQxAEu4B5nvH9wgqd4GjrlmUhjmP5KXp3ZACZHFbEinYNnHoyOUWcIk4RwPfgBveAPOJOwam/KQNEDJDf8AxYEWmALFDIij8LGEAKZEU3UPQEa4e4dwht2PoAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-66"><g><path d="M 833.88 119.94 L 833.8 150" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-67"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 834px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="831" y="129" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-68"><g><path d="M 864.6 120.06 L 864.6 150" fill="none" stroke="#0000cc" style="stroke: light-dark(rgb(0, 0, 204), rgb(212, 212, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-69"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 865px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">3</div></div></div></foreignObject><image x="862" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAAES0lEQVRYR+1XWSh1bRR+DJEMyZR5ukAZMtwRSZkKKSIlEkIpceFCKEkpKSkXXLigZAyJkFkkQ5lFikyZI2PE93/r1d7f2ec43944f3/9nXV1zn7XXs/7Pu9az1pb49dvw79oGmoAMXbVFIkxBDVFP6NoZmYGvb29mJycxPHxMS4vL6GjowNTU1O4u7sjODgYycnJsLS0VAr06R3s7u4iKysLY2NjojvU1dVFTk4OKisrQb/lTQFgbm4OUVFRuLq6Eg0u6+Dn58c2ZGRkJHhPAEBBvby8cHJyInCytrZGeHg47O3tcXt7i/X1ddBG7u/vBX6RkZEYGBhQDkC0NDQ08A56enqora1FWloatLS0BC8eHR0xGuUD9vX1MQY4409wfX0NW1tbPD09sTUNDQ0MDg4iLCxMKVWk9IGBgaBk4CwkJASjo6OKAM3NzUhJSeEXkpKS0NLSInoPKysr8Pb25v0oy2iz+vr6Hxvl+kF2djbq6+t5x+7ubsTGxooCkIOdnR2IMs6Wlpbg6+srBAgICMDs7CzvdH5+DnNzc0kAoaGhGBkZ4X2JWkoKwQlcXV2xs7PDHmpra+P19VVScHJKTExEe3s77z88PAwCFQAUFhbi7u6OPaSCqampkQwQFBSE6elp3n97exsuLi5CAMnR5BzpQq2srPDy8sJWqNCIXq6qfyzX6enpaGxs5GHz8vIEp/82AFVzUVERqLA4I9FbWFhg9cSZJAAKVlVVhYeHB1xcXLBkOD09FZBlYmLCMsnHx0fwXBLA0NAQIiIilF4T0URqamZmpuCjEgBPT0+UlZWxwiSJkTVJAKSc+fn5IO0hag4PD/H+/q6wW1JTUgDZviAJQD4SpeTW1hYqKirQ0dEhWCYQunhOfb8FIBuxv78f0dHR7HScdXZ2Ii4uTjWFRlFyc3NRV1fHA5CET01NqQ6AUtfCwuJP7v++6OfnZzYg/JgiLioBEBBne3t7cHR0/ADo6urCxMQEv5iZmcl681dMXvCoy/n7+38AlJeXo7S0lI9XXV2NgoKCr8RnFby8vMy/s7i4CJo0GEBTUxNSU1P5RWrysgImhkQZZGBggMfHR96VaoU0iQHI91UPDw+sra2JxeXXSf/d3Nz4/6RLdB+ampofALQDZ2dn7O/v805UQPHx8ZJAEhISBAVH/9va2oRpWlxczCqTM8qA1dVVGBoa/hWEqjYmJkbg82nLPDg4AFHDtU16w8HBgU0aXAOXjUJ8l5SUsOYiq0s0EI+Pj/+pCdnPWPnZiPMifmkooD5L4+LGxgY73c3NjWDn1Drn5+f/3nCoS5G2f/X73MnJCT09PQr182kl01yTkZHBvgnEjOSA0po2ZWxsrOCuVCre3t7YjNna2orNzU02cZ+dnYEGYupc9AFCcyhlmo2NjdJ9qEyLlCGoAcRyQHX9QH0Holz/jyn69tklviicVCW+9BU3NYAoW2qK/nuK/gGxK3KGRMumSQAAAABJRU5ErkJggg=="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-70"><g><path d="M 849.2 120.12 L 849.2 150" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-71"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 850px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="847" y="130" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-72"><g><rect x="803" y="90" width="77" height="30" fill="#eeeeee" style="fill: light-dark(rgb(238, 238, 238), rgb(32, 32, 32)); stroke: light-dark(rgb(54, 57, 61), rgb(186, 189, 192));" stroke="#36393d" pointer-events="all"/></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-113"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-109"><g><path d="M 880 287 L 837.34 287.72" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-110"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 288px; margin-left: 860px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="857" y="282" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-111"><g><path d="M 894 276 L 836.54 275.51" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-112"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 276px; margin-left: 867px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="864" y="270" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-103"><g><path d="M 723.46 288.96 L 688 289" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-104"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 289px; margin-left: 707px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="704" y="283" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-99"><g><path d="M 723.23 275.51 L 688 275" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-100"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 276px; margin-left: 707px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="704" y="270" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-93"><g><rect x="723" y="264" width="114" height="59" fill="#f6f5f4" style="fill: light-dark(rgb(246, 245, 244), rgb(237, 237, 237)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 112px; height: 1px; padding-top: 294px; margin-left: 724px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Pulse 2<br />192.168.21.20</div></div></div></foreignObject><image x="724" y="280" width="112" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-118"><g/><g data-cell-id="19RoNj6UBnC00J0asnKw-119"><g><path d="M 1129 284 L 1086.34 284.72" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-120"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 285px; margin-left: 1109px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="1106" y="279" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-121"><g><path d="M 1129 273 L 1085.54 272.51" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-122"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 273px; margin-left: 1109px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="1106" y="267" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-123"><g><path d="M 972.46 285.96 L 937 286" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-124"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 286px; margin-left: 956px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">4</div></div></div></foreignObject><image x="953" y="280" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAACP0lEQVRYR+2XMajpURzHv8pgUVcpg8KqkMRg0FW3ZMGgDKTc4dqUQbIYlMUgi+1KBhsTGRhkYyFhsOiWcgeDUkqI+97/3Oefez3v//j/Le/9f4vk9P2c8/mdczoEHz8LdywBD2CyyytiMgRe0X0V5XI5zGYzAgkEAlAqlWfAm3tQqVTgcrnowFarhcfHR24A8/kcWq0W1OexOAU4nU5Uq9Uvs+UM8Pr6imAweKaCE8BkMoFer8dqteIesN/vYbFY0G63f7s1Wa8gmUwiHo+TcKFQCI1Gg36/z02Tu90uzGYzdrsdCUwkEnh7e0OhUGAPWK/XMBgMGI/HJMxoNBJNLy8v3ABCoRCy2SwJF4lE6PV6UKvVeH5+Zg9oNBqw2+04vgsymQzC4TCBsQYsFgtyWt/f30mg1WpFs9mEQCDgBuDxeFAqlUiYWCzGcDj8cpmxWkGxWITf76d3SD6fJ0pO62bAdDqFTqfDcrkkeQ6HA9TN+b1uAhwOBzw9PYE6lVRJpVKMRiPIZDJuAOl0GpFIhA4rl8twu91n4TftIqqJJpMJm82GBPp8PlC9uFRXKdputyR8MBiQPLlcTtQ8PDxwA4jFYkilUnRYvV6HzWa7GH61Iuoi63Q69OxPt+glSq1WI2fjWF6vFwqFgv4ukUgQjUY/H16ngD9O+4ofVSoVuXH/EcAVK6eHXrVNeQBlgPWr4rtGvgeMG+vuihhn8GvAzX9AeMDfGmAcx/eAV8RogHHA52P/jsUDGOXyiv4DRT8AFPQVhuLjnTAAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-125"><g><path d="M 972.23 272.51 L 926 275" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g><g data-cell-id="19RoNj6UBnC00J0asnKw-126"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 274px; margin-left: 950px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">2</div></div></div></foreignObject><image x="947" y="268" width="6" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAA/CAYAAAD63gh2AAADs0lEQVRYR+1XSyh1URT+rlckxOSWGGAgMkHkGjAwEUpkQoryykCI0pVX5BEGiJJX8ohICjEx8JgRJYVIUYoQwkCK/7d2ne3ue+51znXvP/rPN1Fnr/19e637rbU33Z8v4B9CpwkoVVcrkVKFoJXIvhK9vb1hbW0N8/PzODk5wc3NDe7v7+Ht7Q29Xg+DwYDk5GRkZGTA2dnZopjV32BychLl5eV4fHxUPGVwcDB6e3uRlpYmi5UJfH5+Ijc3FzMzM4rEpgE6nQ4NDQ1oamoS9skEampq0NnZKSOPi4tDTEwM/Pz8cHh4iL29PVxeXsriBgcHUVJSwr8LAtvb20hISBA2hYeHY2JiAtHR0TKyoaEhVFdX4+Xlha+5u7vj9PQUgYGB7JsgkJ2djdnZWR4cGRmJzc1NeHl5WS3X2dkZoqKi8Pr6ymMKCwsxPDwsCtzd3SEgIADv7+9swcXFBQcHB6AMlNDf34+ysjIe5uPjA+JzdXX9zmBkZARFRUU8KCkpCevr60rcbJ3uLH9/f2ZjCZQ5lZuXiCzZ19fHA3p6ephN1SIlJYX1jITx8XHk5eV9C6Snp2NpaYkHbGxsIDExUS0/zN3X1dXFDMAzILKtrS1OeH5+DmogtSBrkqskTE9PIycn51tgYWGBjQEJlB5ZTi2o3mRzCTs7O6xvHDKuaU6R26QHipubG25vb0FucohAVlYWqAISCgoKQK4k2C3Q2tqKuro6Tu7k5ISjoyOEhobaJ0ANSc4hO5uiqqoK3d3d/NOvMtjf32dNSX9NkZmZye4OykKCTQLPz8+or6/HwMAAPj4+BHLq/OXlZXh4eAjfVQvMzc2hoqIC19fXAgHNrObmZlYu05OrzuDq6gqlpaVYWVmRtURYWBhoJMTGxlptlx8zoNFdXFwszHtioju5sbGRTVCamD/BogBdm5WVlcLwY57+uhapwzs6OtilrwYyAfrx8vPzMTU1JeynuUQPgfj4eDW81l1EJzf3NgnSKP/pZrOmKmSwurqK1NRUIbatrQ1Go9GmU5sGcwGqe0REBI6Pj/l6S0uLMAZ+o8IFFhcXQZ0ogS7y3d1di962RYgLmL8oqCstvdRsIWfOk/4J9PX1xdPTE9tP8/zh4QGenp628snimcDFxQWCgoL4IjVPSEiIXeRjY2PsccwELLnHLvavzfTkoQHIBEZHR0GvMUdCEGhvb0dtba0j+cUMHMpsRqb6PvjtITQBxcppJdJKpFgBxQCdYoSdAZqAYgG1Ev0HJfoL+OUVhrOnj6wAAAAASUVORK5CYII="/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-127"><g><rect x="972" y="261" width="114" height="59" fill="#f6f5f4" style="fill: light-dark(rgb(246, 245, 244), rgb(237, 237, 237)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 112px; height: 1px; padding-top: 291px; margin-left: 973px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Pulse 1<br />192.168.21.21</div></div></div></foreignObject><image x="973" y="277" width="112" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-143"><g><path d="M 689 288 L 729 152" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-144"><g><path d="M 878 285 L 850 149" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-145"><g><path d="M 939 286 L 970 150" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-146"><g><path d="M 1129 284 L 1090 150" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g><g data-cell-id="19RoNj6UBnC00J0asnKw-147"><g><path d="M 66 143 L 66 274 L 690 274" fill="none" stroke="#c0bfbc" style="stroke: light-dark(rgb(192, 191, 188), rgb(74, 73, 71));" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g></g></g></g></svg>