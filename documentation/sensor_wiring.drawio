<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:143.0) Gecko/20100101 Firefox/143.0" version="28.2.5">
  <diagram name="Page-1" id="KSMvvk7MSD2y6wT4ytVa">
    <mxGraphModel dx="2210" dy="795" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="19RoNj6UBnC00J0asnKw-149" value="" style="endArrow=none;html=1;rounded=0;strokeWidth=3;edgeStyle=elbowEdgeStyle;elbow=vertical;strokeColor=#C0BFBC;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="95" y="369" as="sourcePoint" />
            <mxPoint x="802" y="503" as="targetPoint" />
            <Array as="points">
              <mxPoint x="457" y="454" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-150" value="" style="endArrow=none;html=1;rounded=0;strokeWidth=3;edgeStyle=elbowEdgeStyle;elbow=vertical;strokeColor=#C0BFBC;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="519" y="380" as="sourcePoint" />
            <mxPoint x="833" y="504" as="targetPoint" />
            <Array as="points">
              <mxPoint x="677" y="401" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-5" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="550" y="230" width="480" height="130" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-1" value="Zone 0" style="rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#9a9996, #1a1a1a);strokeColor=#666666;gradientColor=none;gradientDirection=west;" parent="19RoNj6UBnC00J0asnKw-5" vertex="1">
          <mxGeometry x="360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-2" value="Zone 1" style="rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#9a9996, #1a1a1a);strokeColor=#666666;gradientColor=none;gradientDirection=west;" parent="19RoNj6UBnC00J0asnKw-5" vertex="1">
          <mxGeometry x="240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-3" value="Zone 2" style="rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#9a9996, #1a1a1a);strokeColor=#666666;gradientColor=none;gradientDirection=west;" parent="19RoNj6UBnC00J0asnKw-5" vertex="1">
          <mxGeometry x="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-4" value="Zone 3" style="rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#9a9996, #1a1a1a);strokeColor=#666666;gradientColor=none;gradientDirection=west;" parent="19RoNj6UBnC00J0asnKw-5" vertex="1">
          <mxGeometry width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-73" value="" style="group" parent="19RoNj6UBnC00J0asnKw-5" vertex="1" connectable="0">
          <mxGeometry x="283" y="90" width="77" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-74" value="" style="endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-73" source="19RoNj6UBnC00J0asnKw-82" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="25.666666666666668" y="40" as="sourcePoint" />
            <mxPoint x="15.400000000000006" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-75" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-74" vertex="1" connectable="0">
          <mxGeometry x="-0.0131" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-76" value="" style="endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);" parent="19RoNj6UBnC00J0asnKw-73" source="19RoNj6UBnC00J0asnKw-82" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="32.083333333333336" y="40" as="sourcePoint" />
            <mxPoint x="30.799999999999997" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-77" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-76" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-78" value="" style="endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-73" source="19RoNj6UBnC00J0asnKw-82" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="64.16666666666667" y="30" as="sourcePoint" />
            <mxPoint x="61.599999999999994" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-79" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-78" vertex="1" connectable="0">
          <mxGeometry x="-0.0063" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-80" value="" style="endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-73" source="19RoNj6UBnC00J0asnKw-82" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="55.824999999999996" y="50" as="sourcePoint" />
            <mxPoint x="46.199999999999996" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-81" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-80" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-82" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;" parent="19RoNj6UBnC00J0asnKw-73" vertex="1">
          <mxGeometry width="77" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-83" value="" style="group" parent="19RoNj6UBnC00J0asnKw-5" vertex="1" connectable="0">
          <mxGeometry x="403" y="90" width="77" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-84" value="" style="endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-83" source="19RoNj6UBnC00J0asnKw-92" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="25.666666666666668" y="40" as="sourcePoint" />
            <mxPoint x="15.400000000000006" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-85" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-84" vertex="1" connectable="0">
          <mxGeometry x="-0.0131" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-86" value="" style="endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);" parent="19RoNj6UBnC00J0asnKw-83" source="19RoNj6UBnC00J0asnKw-92" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="32.083333333333336" y="40" as="sourcePoint" />
            <mxPoint x="30.799999999999997" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-87" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-86" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-88" value="" style="endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-83" source="19RoNj6UBnC00J0asnKw-92" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="64.16666666666667" y="30" as="sourcePoint" />
            <mxPoint x="61.599999999999994" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-89" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-88" vertex="1" connectable="0">
          <mxGeometry x="-0.0063" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-90" value="" style="endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-83" source="19RoNj6UBnC00J0asnKw-92" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="55.824999999999996" y="50" as="sourcePoint" />
            <mxPoint x="46.199999999999996" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-91" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-90" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-92" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;" parent="19RoNj6UBnC00J0asnKw-83" vertex="1">
          <mxGeometry width="77" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-6" value="&lt;div&gt;Accumulator&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#b1ddf0;strokeColor=#10739e;" parent="1" vertex="1">
          <mxGeometry x="150" y="230" width="400" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-11" value="&lt;div&gt;Camera&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#b1ddf0;strokeColor=#10739e;" parent="1" vertex="1">
          <mxGeometry x="30" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-12" value="Intake" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#b1ddf0;strokeColor=#10739e;" parent="1" vertex="1">
          <mxGeometry x="-90" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-32" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="-70" y="310" width="77" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-18" value="" style="endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-32" source="19RoNj6UBnC00J0asnKw-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="25.666666666666668" y="40" as="sourcePoint" />
            <mxPoint x="15.400000000000006" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-25" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-18" vertex="1" connectable="0">
          <mxGeometry x="-0.0131" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-19" value="" style="endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);" parent="19RoNj6UBnC00J0asnKw-32" source="19RoNj6UBnC00J0asnKw-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="32.083333333333336" y="40" as="sourcePoint" />
            <mxPoint x="30.799999999999997" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-26" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-19" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-23" value="" style="endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-32" source="19RoNj6UBnC00J0asnKw-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="64.16666666666667" y="30" as="sourcePoint" />
            <mxPoint x="61.599999999999994" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-29" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-23" vertex="1" connectable="0">
          <mxGeometry x="-0.0063" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-24" value="" style="endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-32" source="19RoNj6UBnC00J0asnKw-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="55.824999999999996" y="50" as="sourcePoint" />
            <mxPoint x="46.199999999999996" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-28" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-24" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-17" value="Sensor&lt;br&gt;Intake" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;" parent="19RoNj6UBnC00J0asnKw-32" vertex="1">
          <mxGeometry width="77" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-33" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="50" y="310" width="77" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-34" value="" style="endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-33" source="19RoNj6UBnC00J0asnKw-42" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="25.666666666666668" y="40" as="sourcePoint" />
            <mxPoint x="15.400000000000006" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-35" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-34" vertex="1" connectable="0">
          <mxGeometry x="-0.0131" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-36" value="" style="endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);" parent="19RoNj6UBnC00J0asnKw-33" source="19RoNj6UBnC00J0asnKw-42" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="32.083333333333336" y="40" as="sourcePoint" />
            <mxPoint x="30.799999999999997" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-37" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-36" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-38" value="" style="endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-33" source="19RoNj6UBnC00J0asnKw-42" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="64.16666666666667" y="30" as="sourcePoint" />
            <mxPoint x="61.599999999999994" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-39" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-38" vertex="1" connectable="0">
          <mxGeometry x="-0.0063" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-40" value="" style="endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-33" source="19RoNj6UBnC00J0asnKw-42" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="55.824999999999996" y="50" as="sourcePoint" />
            <mxPoint x="46.199999999999996" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-41" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-40" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-42" value="Sensor&lt;br&gt;Camera" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;" parent="19RoNj6UBnC00J0asnKw-33" vertex="1">
          <mxGeometry width="77" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-43" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="473" y="320" width="77" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-44" value="" style="endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-43" source="19RoNj6UBnC00J0asnKw-52" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="25.666666666666668" y="40" as="sourcePoint" />
            <mxPoint x="15.400000000000006" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-45" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-44" vertex="1" connectable="0">
          <mxGeometry x="-0.0131" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-46" value="" style="endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);" parent="19RoNj6UBnC00J0asnKw-43" source="19RoNj6UBnC00J0asnKw-52" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="32.083333333333336" y="40" as="sourcePoint" />
            <mxPoint x="30.799999999999997" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-47" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-46" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-48" value="" style="endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-43" source="19RoNj6UBnC00J0asnKw-52" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="64.16666666666667" y="30" as="sourcePoint" />
            <mxPoint x="61.599999999999994" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-49" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-48" vertex="1" connectable="0">
          <mxGeometry x="-0.0063" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-50" value="" style="endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-43" source="19RoNj6UBnC00J0asnKw-52" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="55.824999999999996" y="50" as="sourcePoint" />
            <mxPoint x="46.199999999999996" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-51" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-50" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-52" value="Sensor N" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;" parent="19RoNj6UBnC00J0asnKw-43" vertex="1">
          <mxGeometry width="77" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-53" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="593" y="320" width="77" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-54" value="" style="endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-53" source="19RoNj6UBnC00J0asnKw-62" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="25.666666666666668" y="40" as="sourcePoint" />
            <mxPoint x="15.400000000000006" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-55" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-54" vertex="1" connectable="0">
          <mxGeometry x="-0.0131" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-56" value="" style="endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);" parent="19RoNj6UBnC00J0asnKw-53" source="19RoNj6UBnC00J0asnKw-62" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="32.083333333333336" y="40" as="sourcePoint" />
            <mxPoint x="30.799999999999997" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-57" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-56" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-58" value="" style="endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-53" source="19RoNj6UBnC00J0asnKw-62" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="64.16666666666667" y="30" as="sourcePoint" />
            <mxPoint x="61.599999999999994" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-59" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-58" vertex="1" connectable="0">
          <mxGeometry x="-0.0063" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-60" value="" style="endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-53" source="19RoNj6UBnC00J0asnKw-62" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="55.824999999999996" y="50" as="sourcePoint" />
            <mxPoint x="46.199999999999996" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-61" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-60" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-62" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;" parent="19RoNj6UBnC00J0asnKw-53" vertex="1">
          <mxGeometry width="77" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-63" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="713" y="320" width="77" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-64" value="" style="endArrow=none;html=1;rounded=0;exitX=0.199;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;fillColor=#a0522d;strokeColor=light-dark(#986a44, #fcb99e);strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-63" source="19RoNj6UBnC00J0asnKw-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="25.666666666666668" y="40" as="sourcePoint" />
            <mxPoint x="15.400000000000006" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-65" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-64" vertex="1" connectable="0">
          <mxGeometry x="-0.0131" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-66" value="" style="endArrow=none;html=1;rounded=0;exitX=0.401;exitY=0.998;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);" parent="19RoNj6UBnC00J0asnKw-63" source="19RoNj6UBnC00J0asnKw-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="32.083333333333336" y="40" as="sourcePoint" />
            <mxPoint x="30.799999999999997" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-67" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-66" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-68" value="" style="endArrow=none;html=1;rounded=0;exitX=0.8;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeColor=#0000CC;strokeWidth=3;flowAnimation=0;shadow=0;entryX=0.8;entryY=1.002;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-63" source="19RoNj6UBnC00J0asnKw-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="64.16666666666667" y="30" as="sourcePoint" />
            <mxPoint x="61.599999999999994" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-69" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-68" vertex="1" connectable="0">
          <mxGeometry x="-0.0063" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-70" value="" style="endArrow=none;html=1;rounded=0;exitX=0.6;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-63" source="19RoNj6UBnC00J0asnKw-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="55.824999999999996" y="50" as="sourcePoint" />
            <mxPoint x="46.199999999999996" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-71" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-70" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-72" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;" parent="19RoNj6UBnC00J0asnKw-63" vertex="1">
          <mxGeometry width="77" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-113" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="598" y="494" width="192" height="59" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-109" value="" style="endArrow=none;html=1;rounded=0;orthogonal=1;strokeWidth=3;entryX=1.003;entryY=0.402;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-113" target="19RoNj6UBnC00J0asnKw-93" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="192" y="23" as="sourcePoint" />
            <mxPoint x="186" y="34" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-110" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-109" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-111" value="" style="endArrow=none;html=1;rounded=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);entryX=0.996;entryY=0.195;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-113" target="19RoNj6UBnC00J0asnKw-93" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="206" y="12" as="sourcePoint" />
            <mxPoint x="185" y="18" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-112" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-111" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-103" value="" style="endArrow=none;html=1;rounded=0;exitX=0.004;exitY=0.423;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-113" source="19RoNj6UBnC00J0asnKw-93" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="35" y="24" as="sourcePoint" />
            <mxPoint y="25" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-104" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-103" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-99" value="" style="endArrow=none;html=1;rounded=0;exitX=0.002;exitY=0.195;exitDx=0;exitDy=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);exitPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-113" source="19RoNj6UBnC00J0asnKw-93" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="45.200000000000045" y="-120" as="sourcePoint" />
            <mxPoint y="11" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-100" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-99" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-93" value="Pulse 2&lt;br&gt;192.168.21.20" style="rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#f6f5f4, #ededed);" parent="19RoNj6UBnC00J0asnKw-113" vertex="1">
          <mxGeometry x="35" width="114" height="59" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-118" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="847" y="491" width="192" height="59" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-119" value="" style="endArrow=none;html=1;rounded=0;orthogonal=1;strokeWidth=3;entryX=1.003;entryY=0.402;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-118" target="19RoNj6UBnC00J0asnKw-127" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="192" y="23" as="sourcePoint" />
            <mxPoint x="186" y="34" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-120" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-119" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-121" value="" style="endArrow=none;html=1;rounded=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);entryX=0.996;entryY=0.195;entryDx=0;entryDy=0;entryPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-118" target="19RoNj6UBnC00J0asnKw-127" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="192" y="12" as="sourcePoint" />
            <mxPoint x="185" y="18" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-122" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-121" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-123" value="" style="endArrow=none;html=1;rounded=0;exitX=0.004;exitY=0.423;exitDx=0;exitDy=0;exitPerimeter=0;orthogonal=1;strokeWidth=3;" parent="19RoNj6UBnC00J0asnKw-118" source="19RoNj6UBnC00J0asnKw-127" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="35" y="24" as="sourcePoint" />
            <mxPoint y="25" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-124" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-123" vertex="1" connectable="0">
          <mxGeometry x="-0.0253" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-125" value="" style="endArrow=none;html=1;rounded=0;exitX=0.002;exitY=0.195;exitDx=0;exitDy=0;orthogonal=1;strokeWidth=3;strokeColor=light-dark(#c0bfbc, #ededed);exitPerimeter=0;" parent="19RoNj6UBnC00J0asnKw-118" source="19RoNj6UBnC00J0asnKw-127" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="45.200000000000045" y="-120" as="sourcePoint" />
            <mxPoint x="-11" y="14" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-126" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="19RoNj6UBnC00J0asnKw-125" vertex="1" connectable="0">
          <mxGeometry x="-0.0206" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-127" value="Pulse 1&lt;br&gt;192.168.21.21" style="rounded=0;whiteSpace=wrap;html=1;fillColor=light-dark(#f6f5f4, #ededed);" parent="19RoNj6UBnC00J0asnKw-118" vertex="1">
          <mxGeometry x="35" width="114" height="59" as="geometry" />
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-143" value="" style="endArrow=none;html=1;rounded=0;strokeColor=light-dark(#000000,#EDEDED);strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="599" y="518" as="sourcePoint" />
            <mxPoint x="639" y="382" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-144" value="" style="endArrow=none;html=1;rounded=0;strokeColor=light-dark(#000000,#EDEDED);strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="788" y="515" as="sourcePoint" />
            <mxPoint x="760" y="379" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-145" value="" style="endArrow=none;html=1;rounded=0;strokeColor=light-dark(#000000,#EDEDED);strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="849" y="516" as="sourcePoint" />
            <mxPoint x="880" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-146" value="" style="endArrow=none;html=1;rounded=0;strokeColor=light-dark(#000000,#EDEDED);strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1039" y="514" as="sourcePoint" />
            <mxPoint x="1000" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19RoNj6UBnC00J0asnKw-147" value="" style="endArrow=none;html=1;rounded=0;strokeWidth=3;edgeStyle=elbowEdgeStyle;elbow=vertical;strokeColor=#C0BFBC;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-24" y="373" as="sourcePoint" />
            <mxPoint x="600" y="504" as="targetPoint" />
            <Array as="points">
              <mxPoint x="290" y="504" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
