import asyncio
from typing import Any, Callable, Generic, Optional, Type, TypeVar

from asyncua import Client, Node
from asyncua.ua import <PERSON>Val<PERSON>, Variant, VariantType
from loguru import logger
from typing_extensions import deprecated


def convert_value(value: Any, target_type: VariantType) -> Any:
    if isinstance(value, list) or isinstance(value, tuple):
        return [convert_value(i, target_type) for i in value]

    match target_type:
        case VariantType.Boolean:
            return bool(value)
        case VariantType.Float | VariantType.Double:
            return float(value)
        case (
            VariantType.SByte
            | VariantType.Byte
            | VariantType.Int16
            | VariantType.UInt16
            | VariantType.Int32
            | VariantType.UInt32
            | VariantType.Int64
            | VariantType.UInt64
        ):
            return int(value)
        case _:
            return value


T = TypeVar("T")


class OPCUANode(Generic[T]):
    def __init__(self, node: Node, client: "OPCUAClient", node_type: Optional[VariantType] = None) -> None:
        self.node = node
        self._client = client
        self._type = node_type

    async def _rep(self):
        if hasattr(self, "_cached_name"):
            return self._cached_name
        try:
            self._cached_name = await self.node.read_browse_name()
            return self._cached_name

        except Exception:
            return self

    async def get(self) -> T:
        await self._client.loudly_wait_for_connection()
        try:
            res: T = await self.node.read_value()
            logger.trace("Got value of {}: {!r}", await self._rep(), res)
            return res
        except ConnectionError:
            logger.debug("Retrying...")
            await asyncio.sleep(0.5)
            return await self.get()
        except Exception:
            logger.opt(exception=True).critical("Error while reading value of {}", await self._rep())
            raise

    async def update_type(self):
        try:
            self._type = await self.node.read_data_type_as_variant_type()
            logger.trace("Updating opcua type of {} to {}", await self._rep(), self._type)
        except Exception:
            logger.exception("Failed to update opcua type of node {}", await self._rep())

    async def set(self, value: T):
        assert self._type is not None, f"No type for {self.node}"
        await self._client.loudly_wait_for_connection()
        try:
            converted_value: T = convert_value(value, self._type)
            logger.trace(
                "Setting value of {}: {!r} (type: {}, was value: {!r})",
                await self._rep(),
                converted_value,
                self._type,
                value,
            )

            await self.node.write_value(
                DataValue(
                    Variant(converted_value, self._type),
                    SourceTimestamp=None,
                    StatusCode_=None,
                )
            )
        except Exception:
            logger.exception("Error while writing {!r} to {} (type: {})", await self._rep(), value)
            logger.debug("Retrying...")
            await asyncio.sleep(0.5)
            await self.set(value)

    @deprecated("Use set instead")
    async def set_value(self, value: T):
        await self.set(value)

    @deprecated("Use get instead")
    async def value(self) -> T:
        return await self.get()

    async def subscribe(self, callback: Callable[[Any], Any], timeout: float):
        try:
            target_node = self.node
            rep = await self._rep()

            class Subscription:
                def datachange_notification(self, node: Node, val, data):
                    if node != target_node:
                        logger.warning(
                            "Got response for unexpected node {}, expected: {}",
                            rep,
                            target_node,
                        )
                        return

                    logger.trace("datachange_notification received for {}: {!r}", rep, val)

                    callback(val)

            subscription = await self._client._client.create_subscription(timeout, Subscription())
            await subscription.subscribe_data_change(target_node)
        except Exception:
            logger.exception("Error while subscribing to node {}", "node_id")
            raise

    async def wait_for_value(self, value: T, period: float = 0.5) -> T:
        # busy loop for now
        while True:
            try:
                res = await self.get()
                if res == value:
                    return res
                else:
                    await asyncio.sleep(period)
            except Exception:
                logger.exception("Error while waiting for value {}", value)
                raise


async def _find_all_nodes(node: Node) -> list[Node]:
    sub_nodes = await node.get_children()
    sub_sub_nodes_s = await asyncio.gather(*[_find_all_nodes(sub_node) for sub_node in sub_nodes])
    result = [*sub_nodes]
    for sub_sub_nodes in sub_sub_nodes_s:
        result.extend(sub_sub_nodes)
    return result


class OPCUAClient:
    def __init__(self, url, namespace_name: str | int | None = None):
        self._client = Client(url)
        self._is_initialized = False
        self._namespace = namespace_name
        self._is_connected = asyncio.Event()
        self._all_nodes: dict[str, Node] | None = None
        self.disable_reconnect = False

    def _wrap_callback(self, callback):
        def wrapper(*args, **kwargs):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.exception(e)

        return wrapper

    async def initialize(self):
        assert not self._is_initialized, "Client has already been initialized"
        self._is_initialized = True

        # get namespace index
        if isinstance(self._namespace, str):
            self._ns_index = await self._client.get_namespace_index(self._namespace)
        elif isinstance(self._namespace, int):
            self._ns_index = self._namespace
        else:
            self._ns_index = None

        self._reconnect_task = asyncio.create_task(self.reconnect_forever())

    async def connect(self):
        logger.debug("Connecting to OPC UA server...")
        await self._client.connect()
        if not self._is_initialized:
            await self.initialize()
        logger.debug("Connected to OPC UA server")
        self._is_connected.set()

    async def reconnect(self):
        self._is_connected.clear()
        try:
            await self._client.disconnect()
        except Exception:
            pass
        finally:
            await self.connect()

    async def disconnect(self):
        logger.debug("Disconnecting from OPC UA server...")
        self.disable_reconnect = True
        await self._client.disconnect()

    async def connection_ok(self):
        try:
            await self._client.check_connection()
            return True
        except:  # noqa: E722
            return False

    async def reconnect_forever(self):
        while True:
            try:
                await asyncio.sleep(0.2)
                if self.disable_reconnect:
                    break
                if not await self.connection_ok():
                    logger.warning("Connection with OPC UA server lost, reconnecting...")
                    await self.reconnect()
            except Exception:
                logger.exception("Error while reconnecting")
                await asyncio.sleep(5)

    async def loudly_wait_for_connection(self):
        if not self._is_connected.is_set():  # and not self.connection_ok(): # can enable last part to be even more sure
            logger.debug("Waiting for connection...")
            await self._is_connected.wait()
            logger.debug("Connection found")

    async def get_node(self, node_id: str, _t: Type[T]) -> OPCUANode[T]:
        await self.loudly_wait_for_connection()
        if self._ns_index is None:
            node: OPCUANode[T] = OPCUANode(self._client.get_node(node_id), self)
        else:
            if self._all_nodes is None:
                nodes = await _find_all_nodes(self._client.get_objects_node())
                nodes_names = await asyncio.gather(*[n.read_browse_name() for n in nodes])
                self._all_nodes = {f"{name.NamespaceIndex}:{name.Name}": n for name, n in zip(nodes_names, nodes)}
            c_node = self._all_nodes.get(f"{self._ns_index}:{node_id}", None)
            if c_node is None:
                raise ValueError(f"Node with name {node_id!r} not found")
            node: OPCUANode[T] = OPCUANode(c_node, self)

        await node.update_type()
        return node
