import asyncio
from contextlib import asynccontextmanager
from enum import Enum
from typing import TypedDict

import tomlkit

from robot.comm_opcua import OPCU<PERSON>lient, OPCUANode
from pose import Po<PERSON>

from loguru import logger


class Program(Enum):
    IDLE = 0
    PICK_PLACE = 1
    TOUCH_FLY_BY = 2


class Nodes(TypedDict):
    """TypedDict for the nodes. Make sure the name matches that on server side."""

    program_id: OPCUANode[int]
    pre_pick_speed: OPCUANode[int]
    pre_pick_pose_x: OPCUANode[int]
    pre_pick_pose_y: OPCUANode[int]
    pre_pick_pose_z: OPCUANode[int]
    pre_pick_pose_rx: OPCUANode[int]
    pre_pick_pose_ry: OPCUANode[int]
    pre_pick_pose_rz: OPCUANode[int]
    pick_speed: OPCUANode[int]
    pick_pose_x: OPCUANode[int]
    pick_pose_y: OPCUANode[int]
    pick_pose_z: OPCUANode[int]
    pick_pose_rx: OPCUANode[int]
    pick_pose_ry: OPCUANode[int]
    pick_pose_rz: OPCUANode[int]
    fly_by_speed: OPCUANode[int]
    fly_by_pose_x: OPCUANode[int]
    fly_by_pose_y: OPCUANode[int]
    fly_by_pose_z: OPCUANode[int]
    fly_by_pose_rx: OPCUANode[int]
    fly_by_pose_ry: OPCUANode[int]
    fly_by_pose_rz: OPCUANode[int]
    pre_place_speed: OPCUANode[int]
    pre_place_pose_x: OPCUANode[int]
    pre_place_pose_y: OPCUANode[int]
    pre_place_pose_z: OPCUANode[int]
    pre_place_pose_rx: OPCUANode[int]
    pre_place_pose_ry: OPCUANode[int]
    pre_place_pose_rz: OPCUANode[int]
    place_speed: OPCUANode[int]
    place_pose_x: OPCUANode[int]
    place_pose_y: OPCUANode[int]
    place_pose_z: OPCUANode[int]
    place_pose_rx: OPCUANode[int]
    place_pose_ry: OPCUANode[int]
    place_pose_rz: OPCUANode[int]
    robot_conveyor_lock: OPCUANode[bool]
    robot_lost_cargo: OPCUANode[bool]
    robot_unreachable: OPCUANode[bool]
    robot_no_pick: OPCUANode[bool]


class YaskawaRobot:
    def __init__(self, client: OPCUAClient):
        self.client = client
        self.nodes: Nodes

    async def pick_place(self, pick: Pose, fly_by: Pose, place: Pose, speed: float):
        await self.client.loudly_wait_for_connection()

        if not await self.is_idle():
            logger.debug("Waiting for robot to be ready...")
            await self._wait_idle()

        logger.debug("Setting poses")
        await self._set_pre_pick(Pose.from_xyz([0, 0, 0.100]) @ pick, speed)
        await self._set_pick(pick, speed)
        await self._set_fly_by(fly_by, speed)
        await self._set_pre_place(Pose.from_xyz([0, 0, 0.100]) @ place, speed)
        await self._set_place(place, speed)

        logger.debug("Starting pick and place")
        await self._start_program(Program.PICK_PLACE)

        logger.debug("Waiting for robot to finish...")
        await self._wait_idle()
        logger.debug("Pick and place done!")

    async def touch_vertically(self, pose: Pose, speed: float):
        await self.client.loudly_wait_for_connection()

        if not await self.is_idle():
            logger.debug("Waiting for robot to be ready...")
            await self._wait_idle()

        logger.debug("Setting poses")
        pre_pose = pose @ Pose.from_xyz([0, 0, -0.100])
        await self._set_pre_pick(pre_pose, speed)
        await self._set_pick(pose, speed)
        await self._set_fly_by(pre_pose, speed)
        await self._set_pre_place(pre_pose, speed)
        await self._set_place(pre_pose, speed)

        logger.debug("Starting pick and place")
        await self._start_program(Program.PICK_PLACE)
        logger.debug("Waiting for robot to finish...")
        await self._wait_idle()
        logger.debug("Vertical touch done!")

    async def touch(self, pose: Pose, speed: float):
        await self.client.loudly_wait_for_connection()

        if not await self.is_idle():
            logger.debug("Waiting for robot to be ready...")
            await self._wait_idle()

        await self._set_fly_by(pose, speed)
        await self._start_program(Program.TOUCH_FLY_BY)
        await self._wait_idle()

    @asynccontextmanager
    async def connect(self):
        try:
            await self.client.connect()

            # Build the nodes dictionary by calling client.get_node for each node
            self.nodes: Nodes = {}  # type: ignore
            for node_name, node_type in Nodes.__annotations__.items():
                self.nodes[node_name] = await self.client.get_node(node_name, node_type)

            yield self.client
        finally:
            await self.client.disconnect()

    async def _set_pre_pick(self, pose: Pose, speed: float):
        await self._set_node_pose("pre_pick", pose, speed)

    async def _set_pick(self, pose: Pose, speed: float):
        await self._set_node_pose("pick", pose, speed)

    async def _set_fly_by(self, pose: Pose, speed: float):
        await self._set_node_pose("fly_by", pose, speed)

    async def _set_pre_place(self, pose: Pose, speed: float):
        await self._set_node_pose("pre_place", pose, speed)

    async def _set_place(self, pose: Pose, speed: float):
        await self._set_node_pose("place", pose, speed)

    async def _set_node_pose(self, node_basename: str, pose: Pose, speed: float):
        x, y, z, rx, ry, rz = pose.as_xyz_euler("xyz", degrees=True)
        await self.nodes[f"{node_basename}_speed"].set(int(speed * 10000))  # m/s → 0.1mm/s
        await self.nodes[f"{node_basename}_pose_x"].set(int(x * 1e6))  # m → μm
        await self.nodes[f"{node_basename}_pose_y"].set(int(y * 1e6))
        await self.nodes[f"{node_basename}_pose_z"].set(int(z * 1e6))
        await self.nodes[f"{node_basename}_pose_rx"].set(int(rx * 10000))  # deg → 0.0001deg
        await self.nodes[f"{node_basename}_pose_ry"].set(int(ry * 10000))
        await self.nodes[f"{node_basename}_pose_rz"].set(int(rz * 10000))

    async def is_idle(self):
        return await self.nodes["program_id"].get() == Program.IDLE.value

    async def _wait_idle(self):
        await self.nodes["program_id"].wait_for_value(Program.IDLE.value)

    async def _start_program(self, program: Program):
        await self.nodes["program_id"].set(program.value)


def build_production():
    with open("config/config.toml", "r") as f:
        config = tomlkit.load(f)
    return YaskawaRobot(
        OPCUAClient(
            f"opc.tcp://{config['robot']['opcua']['ip']}:{config['robot']['opcua']['port']}/",  # pyright: ignore[reportIndexIssue]
            config["robot"]["opcua"]["namespace"],  # pyright: ignore[reportArgumentType,reportIndexIssue]
        )
    )


async def main():
    robot = build_production()
    async with robot.connect():
        await robot.pick_place(
            pick=Pose.from_xyz_euler([0.100, -1.200, 0.330, -0.180, 0.0, 0.25], seq="xyz"),
            fly_by=Pose.from_xyz_euler([0.800, -0.900, 0.650, -0.180, 0.0, 0.70], seq="xyz"),
            place=Pose.from_xyz_euler([1.760, 0.320, -0.458, -0.180, 0.0, 0.115], seq="xyz"),
            speed=0.1,  # 10 cm/s
        )


if __name__ == "__main__":
    asyncio.run(main())
