from functools import partial

import tomlkit
from loguru import logger

from pose import Po<PERSON>, Rot


def _constrain_angle(min: float, max: float, axis: str, pose: Po<PERSON>) -> Pose:
    """Constrains an angle to a min and max value, wrapping around if necessary at 180°."""
    if axis == "x":
        index = 0
    elif axis == "y":
        index = 1
    elif axis == "z":
        index = 2
    else:
        raise ValueError(f"Invalid axis {axis}")
    angle = pose.R.as_euler("xyz", degrees=True)[index]
    if angle < min:
        correction = [0, 0, 0]
        correction[index] = 180
        pose = pose @ Rot.from_euler("xyz", correction, degrees=True)
    if angle > max:
        correction = [0, 0, 0]
        correction[index] = -180
        pose = pose @ Rot.from_euler("xyz", correction, degrees=True)
    return pose


with open("config/config.toml", "r") as f:
    config = tomlkit.load(f)
min_z_angle: float = config["robot"]["min_z_angle"].unwrap()  # type: ignore
max_z_angle: float = config["robot"]["max_z_angle"].unwrap()  # type: ignore

assert min_z_angle < max_z_angle
assert max_z_angle - min_z_angle >= 180
constrain_z_angle = partial(_constrain_angle, min_z_angle, max_z_angle, "z")
