import asyncio

from controllino.client import build_production as build_controllino
from conveyor.complete_conveyor import CompleteConveyor
from conveyor.frequency_conveyor import FrequencyDrivenConveyor
from conveyor.zoned_conveyor import build_production as build_zoned_conveyor


async def main():
    zoned_conveyor = build_zoned_conveyor()
    controllino = build_controllino()
    frequency_conveyor = FrequencyDrivenConveyor(controllino, zoned_conveyor)
    complete_conveyor = CompleteConveyor(frequency_conveyor, zoned_conveyor, accumulation_gap=5)

    async with zoned_conveyor.connect(), complete_conveyor.connect():
        await zoned_conveyor.set_accumulation(3, True)
        await complete_conveyor.loop_forever()


if __name__ == "__main__":
    asyncio.run(main())
