#include <SPI.h>
#include <Controllino.h>
#include <Ethernet.h>

// Configure a unique MAC address for your device:
byte mac[] = {0xDE, 0xAD, 0xBE, 0xEF, 0xFE, 0xED};
// Set a static IP address appropriate for your network:
IPAddress ip(192, 168, 255, 177);

EthernetServer server(80);

void setup()
{
  // Start Ethernet with static IP
  Ethernet.begin(mac, ip);
  delay(1000); // Give the hardware a moment to settle

  server.begin();
}

void loop()
{
  EthernetClient client = server.available();
  if (!client)
    return;

  String request = client.readStringUntil('\r');
  client.flush();

  String response = "";
  int statusCode = 404;

  if (request.indexOf("GET /heartbeat") >= 0)
  {
    response = "{\"status\":\"alive\"}";
    statusCode = 200;
  }
  else if (request.indexOf("GET /digitalRead?pin=") >= 0)
  {
    int pinStart = request.indexOf("pin=") + 4;
    int pinEnd = request.indexOf(" ", pinStart);
    if (pinEnd == -1)
      pinEnd = request.indexOf("&", pinStart);
    if (pinEnd == -1)
      pinEnd = request.length();

    int pin = request.substring(pinStart, pinEnd).toInt();
    if (pin >= 0 && pin <= 52)
    {
      bool state = digitalRead(pin);
      response = "{\"pin\":" + String(pin) + ",\"state\":" + String(state ? "true" : "false") + "}";
      statusCode = 200;
    }
    else
    {
      response = "{\"error\":\"Invalid pin\"}";
      statusCode = 400;
    }
  }
  else if (request.indexOf("POST /digitalWrite?pin=") >= 0)
  {
    int pinStart = request.indexOf("pin=") + 4;
    int stateStart = request.indexOf("state=") + 6;

    int pin = request.substring(pinStart, request.indexOf("&")).toInt();
    String stateStr = request.substring(stateStart, request.indexOf(" ", stateStart));

    if (pin >= 0 && pin <= 52 && (stateStr == "1" || stateStr == "0"))
    {
      bool state = (stateStr == "1");
      digitalWrite(pin, state);
      response = "{\"pin\":" + String(pin) + ",\"state\":" + String(state ? "true" : "false") + "}";
      statusCode = 200;
    }
    else
    {
      response = "{\"error\":\"Invalid parameters\"}";
      statusCode = 400;
    }
  }
  else if (request.indexOf("POST /setPinMode?pin=") >= 0)
  {
    int pinStart = request.indexOf("pin=") + 4;
    int modeStart = request.indexOf("mode=") + 5;

    int pin = request.substring(pinStart, request.indexOf("&")).toInt();
    String modeStr = request.substring(modeStart, request.indexOf(" ", modeStart));

    if (pin >= 0 && pin <= 52 && (modeStr == "INPUT" || modeStr == "OUTPUT"))
    {
      pinMode(pin, modeStr == "INPUT" ? INPUT : OUTPUT);
      response = "{\"pin\":" + String(pin) + ",\"mode\":\"" + modeStr + "\"}";
      statusCode = 200;
    }
    else
    {
      response = "{\"error\":\"Invalid parameters\"}";
      statusCode = 400;
    }
  }
  else
  {
    response = "{\"error\":\"Not Found\"}";
    statusCode = 404;
  }

  client.println("HTTP/1.1 " + String(statusCode) + (statusCode == 200 ? " OK" : (statusCode == 400 ? " Bad Request" : " Not Found")));
  client.println("Content-Type: application/json");
  client.println("Connection: close");
  client.println();
  client.println(response);

  client.stop();
}
