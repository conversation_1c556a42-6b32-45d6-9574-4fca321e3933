import asyncio
import aiohttp
import tomlkit
from enum import Enum


class PIN_MODE(Enum):
    INPUT = "INPUT"
    OUTPUT = "OUTPUT"


class Controllino:
    def __init__(self, ip: str, port: int = 80):
        self.ip = ip
        self.port = port

    async def is_alive(self) -> bool:
        """
        Checks if the controllino is alive and responsive.
        """
        async with aiohttp.ClientSession() as session:
            async with session.get(f"http://{self.ip}:{self.port}/heartbeat") as resp:
                result = await resp.json()
                if result is None or "status" not in result:
                    return False
                return result["status"] == "alive"

    async def digital_write(self, pin: int, state: bool) -> bool:
        """
        Writes the digital state of a pin.

        Returns True if successful, False otherwise.
        """
        async with aiohttp.ClientSession() as session:
            async with session.post(f"http://{self.ip}:{self.port}/digitalWrite?pin={pin}&state={int(state)}") as resp:
                result = await resp.json()
                if result is None or "state" not in result:
                    return False
                return result["state"] == state

    async def digital_read(self, pin: int) -> bool | None:
        """
        Reads the digital state of a pin.

        Returns None if the request failed.
        """
        async with aiohttp.ClientSession() as session:
            async with session.get(f"http://{self.ip}:{self.port}/digitalRead?pin={pin}") as resp:
                result = await resp.json()
                if result is None or "state" not in result:
                    return None
                return result["state"]

    async def set_pin_mode(self, pin: int, mode: PIN_MODE) -> bool:
        """
        Sets the pin mode.

        Returns True if successful, False otherwise.
        """
        async with aiohttp.ClientSession() as session:
            async with session.post(f"http://{self.ip}:{self.port}/setPinMode?pin={pin}&mode={mode.value}") as resp:
                result = await resp.json()
                if result is None or "mode" not in result:
                    return False
                return result["mode"] == mode.value


def build_production():
    with open("config/config.toml", "r") as f:
        config = tomlkit.load(f)
    return Controllino(
        config["controllino"]["ip"].unwrap(),  # type: ignore
        config["controllino"]["port"].unwrap(),  # type: ignore
    )


async def main():
    controllino = build_production()
    # print(await controllino.is_alive())
    await controllino.set_pin_mode(28, PIN_MODE.OUTPUT)
    await controllino.set_pin_mode(29, PIN_MODE.OUTPUT)
    await controllino.set_pin_mode(30, PIN_MODE.OUTPUT)
    await controllino.digital_write(28, True)
    await controllino.digital_write(29, True)
    await controllino.digital_write(30, True)
    await asyncio.sleep(10)
    await controllino.digital_write(28, False)
    await controllino.digital_write(29, False)
    await controllino.digital_write(30, False)


if __name__ == "__main__":
    asyncio.run(main())
