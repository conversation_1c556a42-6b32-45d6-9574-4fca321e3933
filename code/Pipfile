[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
pybullet = "*"
opencv-python = "*"
numpy = "*"
pybind11 = "*"
cython = "*"
loguru = "*"
open3d-cpu = {file = "../infra/open3d/open3d_cpu-0.19.0+083210b88-cp312-cp312-manylinux_2_39_x86_64.whl"}
pyrealsense2-framos-d400e = {file = "../infra/framos/pyrealsense2_framos_d400e-2.55.10-py3-none-any.whl"}
scipy = "*"
rxpy = "*"
pytest = "*"
tomlkit = "*"
asyncua = "*"
pymodbus = "*"
pybind11-stubgen = "*"
matplotlib = "*"
aiohttp = {extras = ["speedups"], version = "*"}
pytest-asyncio = "*"

[dev-packages]
pytest-cov = "*"
scipy-stubs = "*"

[requires]
python_version = "3.12"
