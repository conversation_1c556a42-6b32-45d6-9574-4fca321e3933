import asyncio

from pose import Pose
from robot.yaskawa import build_production as build_robot


async def main():
    robot = build_robot()
    pose = Pose.from_xyz_euler(
        [1.35981888, -0.52691905, -0.95337146, 179.3055, 0.3521, 90.074], seq="xyz", degrees=True
    )
    async with robot.connect():
        await robot.pick_place(pose, pose, pose, 0.01)


if __name__ == "__main__":
    asyncio.run(main())
