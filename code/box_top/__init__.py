from dataclasses import dataclass, field

import numpy as np
from numpy.typing import NDArray

from pose import Pose


@dataclass
class BoxTop:
    obb_center: Pose
    obb_extent: NDArray[np.floating]
    z_position: float


box_id = 1


@dataclass
class ConveyorBox:
    top: BoxTop
    obb_extent: NDArray[np.floating]
    volume: float
    id: int = field(init=False)
    barcode: str | None = None

    def __post_init__(self):
        global box_id
        self.id = box_id
        box_id += 1
        box_id %= 2**32  # 2 words = 4 bytes
        box_id = max(box_id, 1)  # 0 is reserved for invalid boxes
