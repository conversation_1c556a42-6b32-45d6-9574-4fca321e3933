"""
3D Pose Visualization Utilities

This module provides functions to visualize 3D poses using matplotlib.
A pose consists of a position (translation) and orientation (rotation) in 3D space.
"""

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import sys
import os

# Add the parent directory to the path to import pose
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from pose import Pose


def _set_equal_axis_limits(ax):
    """
    Set axis limits with minimum range of -2 to 2, maintaining 1:1:1 aspect ratio.
    If data extends beyond this range, scale all axes equally.
    """
    # Get current axis limits
    xlim = ax.get_xlim()
    ylim = ax.get_ylim()
    zlim = ax.get_zlim()

    # Find the maximum extent in any direction
    max_range = max(max(abs(xlim[0]), abs(xlim[1])), max(abs(ylim[0]), abs(ylim[1])), max(abs(zlim[0]), abs(zlim[1])))

    # Ensure minimum range of 2 (from -2 to 2)
    max_range = max(max_range, 2.0)

    # Set equal limits for all axes
    ax.set_xlim(-max_range, max_range)
    ax.set_ylim(-max_range, max_range)
    ax.set_zlim(-max_range, max_range)

    # Make axes equal
    ax.set_box_aspect([1, 1, 1])


def vis_pose(pose: Pose, ax=None, scale=1.0, show_origin=True, label=None, colors=None):
    """
    Visualize a 3D pose (position + orientation) using coordinate frame arrows.

    Args:
        pose: Pose object to visualize
        ax: matplotlib 3D axis (if None, creates a new figure)
        scale: Scale factor for the coordinate frame arrows
        show_origin: Whether to show the origin point
        label: Optional label for the pose
        colors: Optional dict with keys 'x', 'y', 'z' for custom axis colors

    Returns:
        ax: The matplotlib 3D axis used for plotting
    """
    if ax is None:
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection="3d")

    # Default colors for coordinate axes
    if colors is None:
        colors = {"x": "red", "y": "green", "z": "blue"}

    # Get pose position and rotation matrix
    position = pose.t
    rotation_matrix = pose.R.as_matrix()

    # Define coordinate frame vectors (scaled)
    x_axis = rotation_matrix[:, 0] * scale  # X-axis
    y_axis = rotation_matrix[:, 1] * scale  # Y-axis
    z_axis = rotation_matrix[:, 2] * scale  # Z-axis

    # Draw coordinate frame arrows
    ax.quiver(
        position[0],
        position[1],
        position[2],
        x_axis[0],
        x_axis[1],
        x_axis[2],
        color=colors["x"],
        arrow_length_ratio=0.1,
        linewidth=2,
    )

    ax.quiver(
        position[0],
        position[1],
        position[2],
        y_axis[0],
        y_axis[1],
        y_axis[2],
        color=colors["y"],
        arrow_length_ratio=0.1,
        linewidth=2,
    )

    ax.quiver(
        position[0],
        position[1],
        position[2],
        z_axis[0],
        z_axis[1],
        z_axis[2],
        color=colors["z"],
        arrow_length_ratio=0.1,
        linewidth=2,
    )

    # Show origin point
    if show_origin:
        vis_pose(
            Pose.identity(),
            ax=ax,
            scale=1.0,
            show_origin=False,
            label="Origin",
            colors={"x": "black", "y": "black", "z": "black"},
        )

    # Add label if provided
    if label:
        ax.text(position[0], position[1], position[2] + scale * 0.2, label, fontsize=10)

    # Set axis labels
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z")

    # Set axis limits with minimum range of -2 to 2, maintaining 1:1:1 aspect ratio
    _set_equal_axis_limits(ax)

    return ax


def vis_multiple_poses(poses, labels=None, scale=1.0, show_origin=True, title="Multiple Poses", figsize=(12, 10)):
    """
    Visualize multiple poses in the same 3D plot.

    Args:
        poses: List of Pose objects to visualize
        labels: Optional list of labels for each pose
        scale: Scale factor for the coordinate frame arrows
        show_origin: Whether to show origin points
        title: Title for the plot
        figsize: Figure size tuple (width, height)

    Returns:
        ax: The matplotlib 3D axis used for plotting
    """
    fig = plt.figure(figsize=figsize)
    ax = fig.add_subplot(111, projection="3d")

    if labels is None:
        labels = [f"Pose {i + 1}" for i in range(len(poses))]

    # Show origin point
    if show_origin:
        vis_pose(
            Pose.identity(),
            ax=ax,
            scale=1.0,
            show_origin=False,
            label="Origin",
            colors={"x": "black", "y": "black", "z": "black"},
        )

    for i, pose in enumerate(poses):
        label = labels[i] if i < len(labels) else f"Pose {i + 1}"
        vis_pose(pose, ax=ax, scale=scale, show_origin=False, label=label)

    ax.set_title(title)

    # Set axis limits with minimum range of -2 to 2, maintaining 1:1:1 aspect ratio
    _set_equal_axis_limits(ax)

    return ax


def vis_pose_trajectory(poses, scale=0.2, show_path=True, path_color="gray", path_alpha=0.6, title="Pose Trajectory"):
    """
    Visualize a trajectory of poses showing the path and orientations.

    Args:
        poses: List of Pose objects representing the trajectory
        scale: Scale factor for the coordinate frame arrows
        show_path: Whether to show the path line connecting poses
        path_color: Color of the path line
        path_alpha: Alpha (transparency) of the path line
        title: Title for the plot

    Returns:
        ax: The matplotlib 3D axis used for plotting
    """
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection="3d")

    # Extract positions for path
    positions = np.array([pose.t for pose in poses])

    # Draw path line if requested
    if show_path and len(poses) > 1:
        ax.plot(
            positions[:, 0],
            positions[:, 1],
            positions[:, 2],
            color=path_color,
            alpha=path_alpha,
            linewidth=2,
        )

    # Draw coordinate frames at each pose (sample every few poses for clarity)
    step = max(1, len(poses) // 20)  # Show at most 20 coordinate frames
    for i in range(0, len(poses), step):
        pose = poses[i]
        label = f"t={i}" if i == 0 or i == len(poses) - 1 else None
        vis_pose(pose, ax=ax, scale=scale, show_origin=False, label=label)

    # Mark start and end points
    if len(poses) > 0:
        ax.scatter(positions[0, 0], positions[0, 1], positions[0, 2], color="green", s=100, marker="o")
        if len(poses) > 1:
            ax.scatter(positions[-1, 0], positions[-1, 1], positions[-1, 2], color="red", s=100, marker="s")

    ax.set_title(title)

    # Set axis limits with minimum range of -2 to 2, maintaining 1:1:1 aspect ratio
    _set_equal_axis_limits(ax)

    return ax


def demo_pose_visualization():
    """Demonstrate the pose visualization functionality"""
    print("Demonstrating pose visualization...")

    # Create some example poses
    pose1 = Pose.identity()  # Identity pose at origin
    pose2 = Pose.from_xyz_rotvec([1, 0, 0, 0, 0, np.pi / 4])  # Translated and rotated
    pose3 = Pose.from_xyz_rotvec([0, 1, 0.5, np.pi / 6, 0, 0])  # Another pose

    # Visualize single pose
    print("1. Visualizing single pose...")
    vis_pose(pose1, label="Identity", show_origin=False)
    plt.title("Single Pose Visualization")
    plt.show()

    # Visualize multiple poses
    print("2. Visualizing multiple poses...")
    poses = [pose2, pose3]
    labels = ["Translated+Rotated", "Another Pose"]
    vis_multiple_poses(poses, labels=labels)
    plt.show()

    # Create a trajectory
    print("3. Visualizing pose trajectory...")
    trajectory_poses = []
    for t in np.linspace(0, 2 * np.pi, 20):
        x = np.cos(t) * 0.5
        y = np.sin(t) * 0.5
        z = t * 0.1
        rx = t * 0.1
        ry = 0
        rz = t * 0.2
        pose = Pose.from_xyz_rotvec([x, y, z, rx, ry, rz])
        trajectory_poses.append(pose)

    vis_pose_trajectory(trajectory_poses, title="Spiral Trajectory")
    plt.show()


if __name__ == "__main__":
    demo_pose_visualization()
