# coroutine.py
#
# A decorator function that takes care of starting a coroutine
# automatically on call.


def coroutine(func):
    def start(*args, **kwargs):
        cr = func(*args, **kwargs)
        next(cr)
        return cr

    return start


# Example use
if __name__ == "__main__":

    @coroutine
    def grep(pattern):
        print(f"Looking for {pattern}")
        while True:
            line = yield
            if pattern in line:
                print(line)

    g = grep("python")
    g.send("Yeah, but no, but yeah, but no")
    g.send("A series of tubes")
    g.send("python generators rock!")
