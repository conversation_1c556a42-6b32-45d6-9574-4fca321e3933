import asyncio

import aiohttp
import pytest
from controllino.client import PIN_MODE, Controllino, build_production as build_controllino


def controllino_available():
    """Check if controllino hardware is available."""
    try:
        controllino = build_controllino()
        asyncio.run(controllino.is_alive())
        return True
    except aiohttp.ClientConnectorError:
        return False


controllino_physical = pytest.mark.skipif(not controllino_available(), reason="Controllino hardware not available")


@pytest.fixture
def controllino():
    yield build_controllino()


def test_controllino_initialization(controllino: Controllino):
    """Test controllino initialization."""
    assert controllino is not None
    assert controllino.ip is not None
    assert controllino.port is not None


@controllino_physical
class TestControllinoPyisical:
    """Test cases for Controllino that require actual hardware."""

    async def test_controllino_is_alive(self, controllino: Controllino):
        """Test controllino is alive."""
        assert await controllino.is_alive()

    async def test_controllino_set_pin_mode(self, controllino: Controllino):
        """Test controllino set pin mode."""
        assert await controllino.set_pin_mode(2, PIN_MODE.OUTPUT)

    async def test_controllino_digital_write(self, controllino: Controllino):
        """Test controllino digital write."""
        await controllino.set_pin_mode(2, PIN_MODE.OUTPUT)
        assert await controllino.digital_write(2, True)
        assert await controllino.digital_write(2, False)

    async def test_controllino_digital_read(self, controllino: Controllino):
        """Test controllino digital read."""
        await controllino.set_pin_mode(2, PIN_MODE.OUTPUT)
        assert await controllino.digital_read(2) is not None

    async def test_controllino_digital_write_persists(self, controllino: Controllino):
        await controllino.set_pin_mode(2, PIN_MODE.OUTPUT)
        await controllino.digital_write(2, True)
        assert await controllino.digital_read(2)
        await controllino.digital_write(2, False)
        assert not await controllino.digital_read(2)

    async def test_controllino_access_mode_idempotent(self, controllino: Controllino):
        await controllino.set_pin_mode(2, PIN_MODE.OUTPUT)

        await controllino.digital_write(2, False)
        assert not await controllino.digital_read(2)
        await controllino.set_pin_mode(2, PIN_MODE.OUTPUT)
        await asyncio.sleep(0.2)  # Give the pin time to change (if it would)
        assert not await controllino.digital_read(2)

        await controllino.digital_write(2, True)
        assert await controllino.digital_read(2)
        await controllino.set_pin_mode(2, PIN_MODE.OUTPUT)
        await asyncio.sleep(0.2)  # Give the pin time to change (if it would)
        assert await controllino.digital_read(2)

    async def test_controllino_set_pin_mode_error(self, controllino: Controllino):
        """Test controllino set pin mode error."""
        assert await controllino.set_pin_mode(1000, PIN_MODE.OUTPUT) is False

    async def test_controllino_digital_write_error(self, controllino: Controllino):
        """Test controllino digital write error."""
        assert await controllino.digital_write(1000, True) is False

    async def test_controllino_digital_read_error(self, controllino: Controllino):
        """Test controllino digital read error."""
        assert await controllino.digital_read(1000) is None
