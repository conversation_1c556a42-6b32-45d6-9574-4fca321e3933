import numpy as np
import open3d as o3d
import pytest
from unittest.mock import Mock, patch
from box_top.box_top_detector import get_box_top, BoxTopDetector
from box_top import BoxTop


def create_synthetic_box_pointcloud(center_x, center_y, center_z, width, depth, height, density=0.01):
    """Create a synthetic point cloud representing a box."""
    points = []

    # Generate points on the top surface of the box
    for x in np.arange(center_x - width / 2, center_x + width / 2, density):
        for y in np.arange(center_y - depth / 2, center_y + depth / 2, density):
            # Add some noise to z to simulate real sensor data
            z = center_z + height / 2 + np.random.normal(0, 0.001)
            points.append([x, y, z])

    # Add some points on the sides for more realistic box shape
    for x in np.arange(center_x - width / 2, center_x + width / 2, density * 2):
        for z in np.arange(center_z - height / 2, center_z + height / 2, density * 2):
            # Left and right sides
            y_left = center_y - depth / 2 + np.random.normal(0, 0.001)
            y_right = center_y + depth / 2 + np.random.normal(0, 0.001)
            points.append([x, y_left, z])
            points.append([x, y_right, z])

    for y in np.arange(center_y - depth / 2, center_y + depth / 2, density * 2):
        for z in np.arange(center_z - height / 2, center_z + height / 2, density * 2):
            # Front and back sides
            x_front = center_x - width / 2 + np.random.normal(0, 0.001)
            x_back = center_x + width / 2 + np.random.normal(0, 0.001)
            points.append([x_front, y, z])
            points.append([x_back, y, z])

    points = np.array(points)
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)

    return pcd


def test_single_box_detection_with_clustering():
    """Test detection of a single box with clustering enabled."""
    scan_line_x = 0.5

    # Create a box near the scan line
    pcd = create_synthetic_box_pointcloud(center_x=0.52, center_y=0.25, center_z=0.36, width=0.3, depth=0.2, height=0.1)

    # Test detection with clustering
    box_top = get_box_top(pcd, scan_line_x=scan_line_x)

    assert box_top is not None
    assert isinstance(box_top, BoxTop)

    # Check that detected center is reasonable
    assert abs(box_top.center[0] - 0.52) < 0.05
    assert abs(box_top.center[1] - 0.25) < 0.05
    assert abs(box_top.center[2] - 0.36) < 0.05

    # Check that dimensions are reasonable
    assert box_top.extent[0] > 0.15  # Length
    assert box_top.extent[1] > 0.1  # Width
    assert box_top.extent[2] > 0.01  # Height (more lenient for synthetic data)


def test_two_boxes_clustering_selects_correct_one():
    """Test that clustering selects the box closest to the scan line."""
    scan_line_x = 0.5

    # Create two boxes: one near scan line, one far
    pcd1 = create_synthetic_box_pointcloud(
        center_x=0.52,
        center_y=0.25,
        center_z=0.36,  # Near scan line
        width=0.25,
        depth=0.15,
        height=0.08,
    )

    pcd2 = create_synthetic_box_pointcloud(
        center_x=0.8,
        center_y=0.25,
        center_z=0.36,  # Far from scan line
        width=0.25,
        depth=0.15,
        height=0.08,
    )

    # Combine point clouds
    combined_points = np.vstack([np.asarray(pcd1.points), np.asarray(pcd2.points)])
    combined_pcd = o3d.geometry.PointCloud()
    combined_pcd.points = o3d.utility.Vector3dVector(combined_points)

    # Test detection with clustering
    box_top = get_box_top(combined_pcd, scan_line_x=scan_line_x)

    assert box_top is not None

    # Should detect the box near the scan line (center_x ≈ 0.52)
    assert abs(box_top.center[0] - 0.52) < abs(box_top.center[0] - 0.8)


def test_detection_without_clustering():
    """Test detection without clustering (scan_line_x=None)."""
    # Create two separate boxes
    pcd1 = create_synthetic_box_pointcloud(
        center_x=0.3,
        center_y=0.25,
        center_z=0.36,
        width=0.2,
        depth=0.15,
        height=0.08,
    )

    pcd2 = create_synthetic_box_pointcloud(
        center_x=0.7,
        center_y=0.25,
        center_z=0.36,
        width=0.2,
        depth=0.15,
        height=0.08,
    )

    # Combine point clouds
    combined_points = np.vstack([np.asarray(pcd1.points), np.asarray(pcd2.points)])
    combined_pcd = o3d.geometry.PointCloud()
    combined_pcd.points = o3d.utility.Vector3dVector(combined_points)

    # Test detection without clustering
    box_top = get_box_top(combined_pcd, scan_line_x=None)

    assert box_top is not None
    # Without clustering, it should detect some combination of both boxes


def test_clustering_with_noise():
    """Test that clustering works correctly with noise points."""
    scan_line_x = 0.5

    # Create main box near scan line
    pcd = create_synthetic_box_pointcloud(
        center_x=0.52,
        center_y=0.25,
        center_z=0.36,
        width=0.25,
        depth=0.15,
        height=0.08,
    )

    # Add noise points far from the main box
    noise_points = np.array(
        [
            [0.1, 0.1, 0.1],
            [0.9, 0.9, 0.9],
            [0.2, 0.8, 0.5],
            [0.8, 0.1, 0.2],
        ]
    )

    # Combine main box with noise
    combined_points = np.vstack([np.asarray(pcd.points), noise_points])
    combined_pcd = o3d.geometry.PointCloud()
    combined_pcd.points = o3d.utility.Vector3dVector(combined_points)

    # Test detection with clustering
    box_top = get_box_top(combined_pcd, scan_line_x=scan_line_x)

    assert box_top is not None

    # Should still detect the main box correctly, ignoring noise
    assert abs(box_top.center[0] - 0.52) < 0.05
    assert abs(box_top.center[1] - 0.25) < 0.05


@patch("box_top.box_top_detector.tomlkit.load")
@patch("builtins.open")
def test_box_top_detector_uses_scan_line(mock_open, mock_toml_load):
    """Test that BoxTopDetector correctly uses scan line from config."""
    # Mock configuration
    mock_config = {
        "calibration": {
            "transform": [
                [0.0, 0.0, 0.0, 0.5],  # scan_line_x = 0.5
                [0.0, 0.0, 0.0, 0.0],
                [0.0, 0.0, 0.0, 0.0],
                [0.0, 0.0, 0.0, 1.0],
            ],
            "roi": [1.0, 1.0],
        },
        "height": {"deadzone_above_conveyor": 0.1},
        "boxes": {"roi_height": 0.5, "min_box_dimension": 0.05},
    }

    # Mock the unwrap method
    for key in mock_config:
        for subkey in mock_config[key]:
            mock_value = Mock()
            mock_value.unwrap.return_value = mock_config[key][subkey]
            mock_config[key][subkey] = mock_value

    mock_toml_load.return_value = mock_config

    # Mock camera
    mock_camera = Mock()

    # Create detector
    detector = BoxTopDetector(mock_camera)

    # Check that scan_line_x is correctly extracted
    assert detector.scan_line_x == 0.5


def test_edge_case_box_exactly_on_scan_line():
    """Test detection when box is exactly centered on the scan line."""
    scan_line_x = 0.5

    # Create box exactly centered on scan line
    pcd = create_synthetic_box_pointcloud(
        center_x=scan_line_x,
        center_y=0.25,
        center_z=0.36,
        width=0.2,
        depth=0.15,
        height=0.08,
    )

    # Test detection
    box_top = get_box_top(pcd, scan_line_x=scan_line_x)

    assert box_top is not None
    assert abs(box_top.center[0] - scan_line_x) < 0.02
