from contextlib import asynccontextmanager

from controllino.client import <PERSON><PERSON>, PIN_MODE
from conveyor.zoned_conveyor import ZonedConveyor


class FrequencyDrivenConveyor:
    def __init__(self, controllino: Controllino, zoned_conveyor: ZonedConveyor):
        self.controllino = controllino
        self.zoned_conveyor = zoned_conveyor

        self.inlet_running = False
        self.camera_running = False
        self.accumulator_running = False

    @asynccontextmanager
    async def connect(self):
        await self.controllino.set_pin_mode(28, PIN_MODE.OUTPUT)
        await self.controllino.set_pin_mode(29, PIN_MODE.OUTPUT)
        await self.controllino.set_pin_mode(30, PIN_MODE.OUTPUT)
        async with self.zoned_conveyor.connect():
            yield self

    async def get_sensor_inlet(self):
        return await self.zoned_conveyor.get_pin2(0)

    async def get_sensor_camera(self):
        return await self.zoned_conveyor.get_pin2(1)

    async def get_sensor_accumulator(self):
        return await self.zoned_conveyor.get_pin2(2)

    async def run_inlet(self, enable: bool):
        if enable == self.inlet_running:
            return
        result = await self.controllino.digital_write(28, enable)
        if not result:
            raise RuntimeError("Failed to run inlet conveyor")
        self.inlet_running = enable

    async def run_camera(self, enable: bool):
        if enable == self.camera_running:
            return
        result = await self.controllino.digital_write(29, enable)
        if not result:
            raise RuntimeError("Failed to run camera conveyor")
        self.camera_running = enable

    async def run_accumulator(self, enable: bool):
        if enable == self.accumulator_running:
            return
        result = await self.controllino.digital_write(30, enable)
        if not result:
            raise RuntimeError("Failed to run accumulator conveyor")
        self.accumulator_running = enable
