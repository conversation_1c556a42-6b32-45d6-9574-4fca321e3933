import asyncio
from contextlib import AsyncExitStack, asynccontextmanager
from pprint import pprint

import tomlkit

from conveyor.conveylinx import ConveyLinx, ConveyorStopStatus, ModuleStatus, Zone, ZoneStatus
from loguru import logger


class ZonedConveyor:
    """
    Class to manage a conveyor with multiple zones where every controller controls two zones.
    List of controllers should be ordered from upstream to downstream.
    """

    def __init__(self, controllers: list[ConveyLinx]):
        self.controllers = controllers
        self.num_zones = len(controllers) * 2
        self.polling_interval = 0.1

    async def get_zone_status(self, zone) -> ZoneStatus:
        controller_index = zone // 2
        controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
        return await self.controllers[controller_index].get_local_status(controller_zone)

    async def get_module_status(self) -> list[ModuleStatus]:
        return [await controller.get_module_status() for controller in self.controllers]

    async def jog_forward(self, zone: int = None, enable=True) -> None:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.jog_forward(Zone.UPSTREAM, enable)
                await controller.jog_forward(Zone.DOWNSTREAM, enable)
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            await self.controllers[controller_index].jog_forward(controller_zone, enable)

    async def jog_reverse(self, zone: int = None, enable=True) -> None:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.jog_reverse(Zone.UPSTREAM, enable)
                await controller.jog_reverse(Zone.DOWNSTREAM, enable)
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            await self.controllers[controller_index].jog_reverse(controller_zone, enable)

    async def set_accumulation(self, zone: int = None, enable=True) -> None:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.set_accumulation(Zone.UPSTREAM, enable)
                await controller.set_accumulation(Zone.DOWNSTREAM, enable)
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            await self.controllers[controller_index].set_accumulation(controller_zone, enable)

    async def set_maintenance_mode(self, zone: int = None, enable=True) -> None:
        """
        Set maintenance mode. This will stop the conveyor and disable all motion in this zone only.
        Notice that this happens in a "dirty" way: the controller might see the effects as a jam
        when it would have wanted to discharge from this zone to downstream.

        You can use this for blocking upstream if you're only clearing this zone but no downstream zones.
        """
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.set_maintenance_mode(Zone.UPSTREAM, enable)
                await controller.set_maintenance_mode(Zone.DOWNSTREAM, enable)
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            await self.controllers[controller_index].set_maintenance_mode(controller_zone, enable)

    async def get_maintenance_mode(self, zone: int = None) -> list[bool]:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            return [(await self.get_maintenance_mode(zone))[0] for zone in range(self.num_zones)]
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            return [await self.controllers[controller_index].get_maintenance_mode(controller_zone)]

    async def release_and_accumulate(self, zone: int) -> bool:
        """
        Release and accumulate a box in the given zone.
        Returns True if successful, False if no box to discharge.
        """
        controller_index = zone // 2
        controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
        zone_status = await self.controllers[controller_index].get_local_status(controller_zone)
        if zone_status != ZoneStatus.BLOCKED_STOPPED:
            logger.warning("Trying to discharge non existing box!")
            return False
        await self.controllers[controller_index].release_and_accumulate(controller_zone)
        return True

    async def release_and_accumulate_downstream_discharge(self) -> bool:
        """
        Release and accumulate the downstream discharge zone.
        Returns True if successful, False if no box to discharge.
        """
        return await self.release_and_accumulate(self.num_zones - 1)

    async def is_upstream_induct_free(self) -> bool:
        return await self._is_status(0, [ZoneStatus.CLEAR_STOPPED, ZoneStatus.BLOCKED_RUNNING])

    async def wait_upstream_induct_free(self) -> None:
        while not await self.is_upstream_induct_free():
            await asyncio.sleep(self.polling_interval)

    async def upstream_induct_start(self) -> None:
        await self.controllers[0].upstream_induct_start()

    async def upstream_induct_end(self, tracking_word_1: int = 0, tracking_word_2: int = 0) -> None:
        await self.controllers[0].upstream_induct_end(tracking_word_1, tracking_word_2)

    async def get_tracking(self, zone: int) -> tuple[int, int]:
        assert 0 <= zone < self.num_zones
        controller_index = zone // 2
        controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
        return await self.controllers[controller_index].get_local_tracking(controller_zone)

    async def _is_status(self, zone: int | None, status: ZoneStatus | list[ZoneStatus]) -> bool:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for zone_num in range(self.num_zones):
                if not await self._is_status(zone_num, status):
                    return False
            return True
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            current_status = await self.controllers[controller_index].get_local_status(controller_zone)
            if isinstance(status, list):
                return current_status in status
            return current_status == status

    async def is_stopped(self, zone: int = None) -> bool:
        return await self._is_status(zone, [ZoneStatus.CLEAR_STOPPED, ZoneStatus.BLOCKED_STOPPED, ZoneStatus.BUSY])

    async def wait_stopped(self, zone: int = None) -> None:
        while not await self.is_stopped(zone):
            await asyncio.sleep(self.polling_interval)

    async def is_blocked_stopped(self, zone: int = None) -> bool:
        return await self._is_status(zone, ZoneStatus.BLOCKED_STOPPED)

    async def is_all_downstream_blocked_stopped(self, zone_from: int) -> bool:
        for zone in range(zone_from, self.num_zones):
            if not await self.is_blocked_stopped(zone):
                return False
        return True

    async def wait_all_downstream_blocked_stopped(self, zone_from: int) -> None:
        while not await self.is_all_downstream_blocked_stopped(zone_from):
            await asyncio.sleep(self.polling_interval)

    async def set_conveystop(self, zone: int = None, enable: bool = True) -> None:
        """
        Set convey stop. This will stop the conveyor and disable all motion for the whole module.
        This happens in a cleaner way than maintenance mode: the controller will not get confused
        by its own actions. The downside is that the granularity is on the module level instead of
        the zone level (compared with using maintenance mode).
        """
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.set_conveystop(enable)
        else:
            controller_index = zone // 2
            await self.controllers[controller_index].set_conveystop(enable)

    async def get_convey_stop_status(self, zone: int = None) -> list[ConveyorStopStatus]:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            return [await controller.get_convey_stop_status() for controller in self.controllers]
        else:
            controller_index = zone // 2
            return [await self.controllers[controller_index].get_convey_stop_status()]

    async def get_filled_zones(self) -> list[int]:
        return [i for i in range(self.num_zones) if await self.is_blocked_stopped(i)]

    async def enable_all_pin2_as_input(self):
        for controller in self.controllers:
            await controller.enable_left_pin2_as_input()
            await controller.enable_right_pin2_as_input()

    async def get_pin2(self, zone: int) -> bool:
        controller_index = zone // 2
        if zone % 2 == 0:
            return await self.controllers[controller_index].get_left_sensor_pin2()
        else:
            return await self.controllers[controller_index].get_right_sensor_pin2()

    async def get_pin4(self, zone: int) -> bool:
        controller_index = zone // 2
        if zone % 2 == 0:
            return await self.controllers[controller_index].get_left_sensor_pin4()
        else:
            return await self.controllers[controller_index].get_right_sensor_pin4()

    async def get_heartbeat(self) -> list[bool]:
        return [await controller.get_heartbeat() for controller in self.controllers]

    @asynccontextmanager
    async def wait_box_lock_zone(self, zone: int):
        # NOTE: Using conveystop now, in some situations it might be less time efficient,
        # but it will also not break the ZPA.
        await self.wait_all_downstream_blocked_stopped(zone)
        # await self.set_maintenance_mode(zone, True)
        await self.set_conveystop(zone, True)
        yield
        # await self.set_maintenance_mode(zone, False)
        await self.set_conveystop(zone, False)

    @asynccontextmanager
    async def connect(self):
        async with AsyncExitStack() as stack:
            for controller in self.controllers:
                await stack.enter_async_context(controller.connect())
            if any(await self.get_maintenance_mode(None)):
                logger.warning("At least one zone of the conveyor is in maintenance mode!")
            if any([i["stop_active_due_to_stop_command"] for i in await self.get_convey_stop_status()]):
                logger.warning("At least one zone of the conveyor is in convey stop mode!")
            await self.enable_all_pin2_as_input()
            yield self
            await self.upstream_induct_end(0, 0)  # Make sure the upstream induct will not run forever


def build_production():
    with open("config/config.toml", "r") as f:
        config = tomlkit.load(f)

    return ZonedConveyor([ConveyLinx(ip_address) for ip_address in config["conveyor"]["ip_addresses"].unwrap()])  # pyright: ignore[reportIndexIssue]


async def main():
    conveyor = build_production()
    async with conveyor.connect() as conveyor:
        # logger.debug("Connected to conveyor")
        # await conveyor.set_accumulation(2, True)
        # pprint(await conveyor.get_module_status())
        # pprint(await conveyor.get_tracking(2))
        # await conveyor.release_and_accumulate_downstream_discharge()
        # await conveyor.upstream_induct(True, 3, 6)
        # # # # pprint(await conveyor.get_tracking(3))
        # # # # await asyncio.sleep(10)
        # # # await conveyor.upstream_induct(False)
        # while not await conveyor.is_stopped():
        #     pprint(await conveyor.get_tracking())
        #     await asyncio.sleep(0.4)
        # pprint(await conveyor.get_tracking())
        # async with conveyor.wait_box_lock_zone(2):
        #     pprint(await conveyor.get_convey_stop_status())
        #     await asyncio.sleep(20)
        # pprint([i["stop_active_due_to_stop_command"] for i in await conveyor.get_convey_stop_status()])
        # await conveyor.set_conveystop(None, False)
        # pprint([i["stop_active_due_to_stop_command"] for i in await conveyor.get_convey_stop_status()])
        # pprint(await conveyor.get_maintenance_mode(None))
        pprint([pin2 for pin2 in await asyncio.gather(*[conveyor.get_pin2(i) for i in range(conveyor.num_zones)])])
        pprint([pin4 for pin4 in await asyncio.gather(*[conveyor.get_pin4(i) for i in range(conveyor.num_zones)])])
        pprint(await conveyor.get_heartbeat())
        await conveyor.enable_all_pin2_as_input()
        pprint([pin2 for pin2 in await asyncio.gather(*[conveyor.get_pin2(i) for i in range(conveyor.num_zones)])])
        pprint([pin4 for pin4 in await asyncio.gather(*[conveyor.get_pin4(i) for i in range(conveyor.num_zones)])])
        pprint(await conveyor.get_heartbeat())
        await asyncio.sleep(1)
        pprint([pin2 for pin2 in await asyncio.gather(*[conveyor.get_pin2(i) for i in range(conveyor.num_zones)])])
        pprint([pin4 for pin4 in await asyncio.gather(*[conveyor.get_pin4(i) for i in range(conveyor.num_zones)])])
        pprint(await conveyor.get_heartbeat())


if __name__ == "__main__":
    asyncio.run(main())
