import asyncio
import time
from contextlib import asynccontextmanager

from loguru import logger

from box_top import ConveyorBox
from conveyor.conveylinx import ZoneStatus, isRunning
from conveyor.frequency_conveyor import FrequencyDrivenConveyor
from conveyor.zoned_conveyor import ZonedConveyor


class CompleteConveyor:
    """
    A class to manage the complete conveyor system, including the frequency conveyor and the zoned conveyor.

    - accumulation gap: seconds, set to infinity to get the box as fast as possible to the zoned conveyor
    """

    def __init__(
        self,
        frequency_conveyor: FrequencyDrivenConveyor,
        zoned_conveyor: ZonedConveyor,
        accumulation_gap: int | float = float("inf"),
    ):
        self.frequency_conveyor = frequency_conveyor
        self.zoned_conveyor = zoned_conveyor

        self.accumulation_gap = accumulation_gap
        self.unconfirmed_box_timeout = 10
        self._camera_lock = False
        self.camera_box: ConveyorBox | None = None
        self.accumulator_boxes: list[ConveyorBox | None] = []

    async def loop_forever(self):
        inlet_last_unconfirmed_box_release = 0
        prev_camera_state = None
        inlet_state = ZoneStatus.CLEAR_RUNNING
        camera_state = ZoneStatus.CLEAR_STOPPED
        accumulator_state = ZoneStatus.CLEAR_STOPPED
        accumulator_clear_running_started = 0

        flicker_means_pass_timeout = 1.5
        inlet_stopped_at = 0
        accumulator_stopped_at = 0
        camera_stopped_at = 0

        while True:
            now = time.monotonic()

            sensors = {
                "inlet": await self.frequency_conveyor.get_sensor_inlet(),
                "camera": await self.frequency_conveyor.get_sensor_camera(),
                "accumulator": await self.frequency_conveyor.get_sensor_accumulator(),
            }

            ### Accumulator ###

            if accumulator_state == ZoneStatus.CLEAR_RUNNING and sensors["accumulator"]:
                logger.info("Accumulator: New box at end")
                accumulator_state = ZoneStatus.BLOCKED_STOPPED

            if accumulator_state == ZoneStatus.BLOCKED_STOPPED:
                if not sensors["accumulator"]:
                    if accumulator_stopped_at + flicker_means_pass_timeout < now:
                        accumulator_state = ZoneStatus.BLOCKED_RUNNING  # box probably went forward: goto exit clause
                    else:
                        logger.warning("Accumulator: Box removed! Is this sensor flicker or human interaction?")
                        accumulator_state = ZoneStatus.CLEAR_RUNNING

                if await self.zoned_conveyor.is_upstream_induct_free():
                    logger.info("Accumulator: Releasing box")
                    await self.zoned_conveyor.upstream_induct_start()
                    accumulator_state = ZoneStatus.BLOCKED_RUNNING

            if accumulator_state == ZoneStatus.BLOCKED_RUNNING and sensors["accumulator"]:
                upstream_state = await self.zoned_conveyor.get_zone_status(0)
                if upstream_state not in [
                    ZoneStatus.CLEAR_RUNNING,
                    ZoneStatus.BLOCKED_RUNNING,
                ]:
                    logger.info("accumulator: Zoned conveyor stopped halfway transition!")
                    accumulator_state = ZoneStatus.BLOCKED_STOPPED
                    accumulator_stopped_at = now

            if accumulator_state == ZoneStatus.BLOCKED_RUNNING and not sensors["accumulator"]:
                logger.info("Accumulator: Box exited")
                if len(self.accumulator_boxes) == 0:
                    logger.error("Accumulator: Unknown box! (list empty)")
                    await self.zoned_conveyor.upstream_induct_end(0, 0)
                else:
                    box = self.accumulator_boxes.pop(0)
                    if box is None:
                        logger.error("Accumulator: Unknown box! (list contained None)")
                        await self.zoned_conveyor.upstream_induct_end(0, 0)
                    else:
                        await self.zoned_conveyor.upstream_induct_end(box.id & 0xFFFF, box.id >> 16)
                if len(self.accumulator_boxes) == 0 and not sensors["camera"]:
                    logger.info("Accumulator is empty, stopping")
                    accumulator_state = ZoneStatus.CLEAR_STOPPED
                else:
                    accumulator_state = ZoneStatus.CLEAR_RUNNING

            if accumulator_state is not ZoneStatus.CLEAR_RUNNING:
                accumulator_clear_running_started = now
            if (
                accumulator_state is ZoneStatus.CLEAR_RUNNING
                and accumulator_clear_running_started + self.accumulation_gap < now
            ):
                logger.info("Accumulation gap reached, stopping accumulator to wait for next box")
                accumulator_state = ZoneStatus.CLEAR_STOPPED

            ### Camera ###

            if self._camera_lock and camera_state != ZoneStatus.BUSY:
                logger.info(f"Camera lock active, saving current state {camera_state}")
                prev_camera_state = camera_state
                camera_state = ZoneStatus.BUSY
            elif (not self._camera_lock) and camera_state == ZoneStatus.BUSY:
                logger.info(f"Camera lock released, restoring previous state {prev_camera_state}")
                camera_state = prev_camera_state
                prev_camera_state = None

            if camera_state == ZoneStatus.CLEAR_RUNNING and sensors["camera"]:
                logger.info("Camera: New box")
                camera_state = ZoneStatus.BLOCKED_STOPPED

            if camera_state == ZoneStatus.BLOCKED_STOPPED:
                if not sensors["camera"]:
                    if camera_stopped_at + flicker_means_pass_timeout < now:
                        camera_state = ZoneStatus.BLOCKED_RUNNING  # box probably went forward: goto exit clause
                    else:
                        logger.warning("Camera: Box removed! Is this sensor flicker or human interaction?")
                        camera_state = ZoneStatus.CLEAR_RUNNING

                if accumulator_state in [
                    ZoneStatus.CLEAR_RUNNING,
                    ZoneStatus.CLEAR_STOPPED,
                    ZoneStatus.BLOCKED_RUNNING,
                ]:
                    logger.info("Camera: Releasing box")
                    camera_state = ZoneStatus.BLOCKED_RUNNING
                    if accumulator_state == ZoneStatus.CLEAR_STOPPED:
                        logger.debug("Camera: Woke up accumulator")
                        accumulator_state = ZoneStatus.CLEAR_RUNNING

            if camera_state == ZoneStatus.BLOCKED_RUNNING and sensors["camera"]:
                if accumulator_state not in [
                    ZoneStatus.CLEAR_RUNNING,
                    ZoneStatus.BLOCKED_RUNNING,
                ]:
                    logger.info("Camera: Accumulator stopped halfway transition!")
                    camera_state = ZoneStatus.BLOCKED_STOPPED
                    camera_stopped_at = now

            if camera_state == ZoneStatus.BLOCKED_RUNNING and not sensors["camera"]:
                logger.info("Camera: Box exited")
                if self.camera_box is None:
                    logger.error("No box at camera!")
                self.accumulator_boxes.append(self.camera_box)
                self.camera_box = None

                inlet_last_unconfirmed_box_release = 0
                if not sensors["inlet"]:
                    camera_state = ZoneStatus.CLEAR_STOPPED
                else:
                    camera_state = ZoneStatus.CLEAR_RUNNING

            ### Inlet ###

            if inlet_state == ZoneStatus.CLEAR_RUNNING and sensors["inlet"]:
                logger.info("Inlet: New box")
                inlet_state = ZoneStatus.BLOCKED_STOPPED

            if inlet_state == ZoneStatus.BLOCKED_STOPPED:
                if not sensors["inlet"]:
                    if inlet_stopped_at + flicker_means_pass_timeout < now:
                        inlet_state = ZoneStatus.BLOCKED_RUNNING  # box probably went forward: goto exit clause
                    else:
                        logger.warning("Inlet: Box removed! Is this sensor flicker or human interaction?")
                        inlet_state = ZoneStatus.CLEAR_RUNNING

                if (
                    camera_state
                    in [
                        ZoneStatus.CLEAR_RUNNING,
                        ZoneStatus.CLEAR_STOPPED,
                        ZoneStatus.BLOCKED_RUNNING,
                    ]
                    and inlet_last_unconfirmed_box_release + self.unconfirmed_box_timeout < now
                ):
                    logger.info("Inlet: Releasing box")
                    inlet_state = ZoneStatus.BLOCKED_RUNNING
                    inlet_last_unconfirmed_box_release = now
                    if camera_state == ZoneStatus.CLEAR_STOPPED:
                        logger.debug("Inlet: Woke up camera")
                        camera_state = ZoneStatus.CLEAR_RUNNING

            if inlet_state == ZoneStatus.BLOCKED_RUNNING and sensors["inlet"]:
                if camera_state not in [
                    ZoneStatus.CLEAR_RUNNING,
                    ZoneStatus.BLOCKED_RUNNING,
                ]:
                    logger.info("Inlet: Camera stopped halfway transition!")
                    inlet_state = ZoneStatus.BLOCKED_STOPPED
                    inlet_stopped_at = now

            if inlet_state == ZoneStatus.BLOCKED_RUNNING and not sensors["inlet"]:
                logger.info("Inlet: Box exited")
                inlet_state = ZoneStatus.CLEAR_RUNNING

            await self.frequency_conveyor.run_inlet(isRunning(inlet_state))
            await self.frequency_conveyor.run_camera(isRunning(camera_state))
            await self.frequency_conveyor.run_accumulator(isRunning(accumulator_state))
            await asyncio.sleep(0.05)

    @asynccontextmanager
    async def connect(self):
        async with self.frequency_conveyor.connect(), self.zoned_conveyor.connect():
            try:
                yield self
            finally:
                logger.debug("Stopping all frequency conveyors because exit program")
                await self.frequency_conveyor.run_inlet(False)
                await self.frequency_conveyor.run_camera(False)
                await self.frequency_conveyor.run_accumulator(False)

    @asynccontextmanager
    async def wait_pick_zone_lock(self, zone: int):
        async with self.zoned_conveyor.wait_box_lock_zone(zone):
            yield

    @asynccontextmanager
    async def wait_camera_lock(self):
        self._camera_lock = True
        yield
        self._camera_lock = False

    def set_camera_box(self, box: ConveyorBox):
        if self.camera_box is not None:
            logger.error("Overwriting camera box!")
        self.camera_box = box
